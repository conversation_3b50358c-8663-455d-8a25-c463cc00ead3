@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-neutral-50 text-neutral-900 font-sans;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
  
  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  /* Custom scrollbar styles */
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: #cbd5e1 #f1f5f9;
  }
  
  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  
  .custom-scrollbar::-webkit-scrollbar-track {
    @apply bg-neutral-100 rounded-full;
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb {
    @apply bg-neutral-300 rounded-full hover:bg-neutral-400;
  }
  
  /* Button variants */
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-medium px-4 py-2 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }
  
  .btn-secondary {
    @apply bg-neutral-200 hover:bg-neutral-300 text-neutral-900 font-medium px-4 py-2 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-neutral-500 focus:ring-offset-2;
  }
  
  .btn-outline {
    @apply border border-primary-600 text-primary-600 hover:bg-primary-50 font-medium px-4 py-2 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }
  
  .btn-danger {
    @apply bg-danger-600 hover:bg-danger-700 text-white font-medium px-4 py-2 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-danger-500 focus:ring-offset-2;
  }
  
  /* Card styles */
  .card {
    @apply bg-white rounded-xl shadow-soft border border-neutral-200 p-6;
  }
  
  .card-header {
    @apply border-b border-neutral-200 pb-4 mb-6;
  }
  
  /* Form styles */
  .form-input {
    @apply w-full px-3 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200;
  }
  
  .form-label {
    @apply block text-sm font-medium text-neutral-700 mb-2;
  }
  
  .form-error {
    @apply text-danger-600 text-sm mt-1;
  }
  
  /* Status badges */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .badge-success {
    @apply bg-success-100 text-success-800;
  }
  
  .badge-warning {
    @apply bg-warning-100 text-warning-800;
  }
  
  .badge-danger {
    @apply bg-danger-100 text-danger-800;
  }
  
  .badge-info {
    @apply bg-primary-100 text-primary-800;
  }
  
  /* Loading animations */
  .loading-spinner {
    @apply animate-spin rounded-full border-2 border-neutral-200 border-t-primary-600;
  }
  
  .loading-pulse {
    @apply animate-pulse bg-neutral-200 rounded;
  }
  
  /* Hover effects */
  .hover-lift {
    @apply transition-transform duration-200 hover:scale-105;
  }
  
  .hover-glow {
    @apply transition-shadow duration-200 hover:shadow-medium;
  }
}

@layer utilities {
  .text-gradient {
    @apply bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent;
  }
  
  .bg-gradient-primary {
    @apply bg-gradient-to-r from-primary-600 to-primary-700;
  }
  
  .bg-gradient-secondary {
    @apply bg-gradient-to-r from-secondary-500 to-secondary-600;
  }
  
  .bg-gradient-accent {
    @apply bg-gradient-to-r from-accent-500 to-accent-600;
  }
}
