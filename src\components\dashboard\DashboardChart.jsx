import React from 'react'
import { 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  AreaChart,
  Area
} from 'recharts'
import Card from '@components/ui/Card'
import Button from '@components/ui/Button'
import { TrendingUp, Calendar } from 'lucide-react'

const DashboardChart = () => {
  const [chartType, setChartType] = React.useState('inventory')
  const [timeRange, setTimeRange] = React.useState('7d')
  
  // Mock data for inventory value over time
  const inventoryData = [
    { date: 'Jan 1', value: 2650000, items: 1180 },
    { date: 'Jan 2', value: 2680000, items: 1195 },
    { date: 'Jan 3', value: 2720000, items: 1210 },
    { date: 'Jan 4', value: 2750000, items: 1225 },
    { date: 'Jan 5', value: 2780000, items: 1240 },
    { date: 'Jan 6', value: 2820000, items: 1255 },
    { date: 'Jan 7', value: 2847392, items: 1247 }
  ]
  
  // Mock data for stock movements
  const movementData = [
    { date: 'Jan 1', inbound: 45, outbound: 32, net: 13 },
    { date: 'Jan 2', inbound: 52, outbound: 28, net: 24 },
    { date: 'Jan 3', inbound: 38, outbound: 41, net: -3 },
    { date: 'Jan 4', inbound: 61, outbound: 35, net: 26 },
    { date: 'Jan 5', inbound: 43, outbound: 39, net: 4 },
    { date: 'Jan 6', inbound: 55, outbound: 33, net: 22 },
    { date: 'Jan 7', inbound: 48, outbound: 29, net: 19 }
  ]
  
  const formatCurrency = (value) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value)
  }
  
  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-neutral-200 rounded-lg shadow-lg">
          <p className="font-medium text-neutral-900 mb-2">{label}</p>
          {payload.map((entry, index) => (
            <p key={index} className="text-sm" style={{ color: entry.color }}>
              {entry.name}: {
                chartType === 'inventory' && entry.dataKey === 'value'
                  ? formatCurrency(entry.value)
                  : entry.value
              }
            </p>
          ))}
        </div>
      )
    }
    return null
  }
  
  return (
    <Card>
      <Card.Header>
        <div className="flex items-center justify-between">
          <div>
            <Card.Title className="flex items-center">
              <TrendingUp className="w-5 h-5 mr-2 text-primary-600" />
              {chartType === 'inventory' ? 'Inventory Trends' : 'Stock Movements'}
            </Card.Title>
            <Card.Description>
              {chartType === 'inventory' 
                ? 'Track inventory value and item count over time'
                : 'Monitor inbound and outbound stock movements'
              }
            </Card.Description>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button
              variant={chartType === 'inventory' ? 'primary' : 'ghost'}
              size="sm"
              onClick={() => setChartType('inventory')}
            >
              Inventory
            </Button>
            <Button
              variant={chartType === 'movements' ? 'primary' : 'ghost'}
              size="sm"
              onClick={() => setChartType('movements')}
            >
              Movements
            </Button>
          </div>
        </div>
      </Card.Header>
      
      <div className="h-80">
        <ResponsiveContainer width="100%" height="100%">
          {chartType === 'inventory' ? (
            <AreaChart data={inventoryData}>
              <defs>
                <linearGradient id="valueGradient" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.3}/>
                  <stop offset="95%" stopColor="#3b82f6" stopOpacity={0}/>
                </linearGradient>
                <linearGradient id="itemsGradient" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#06b6d4" stopOpacity={0.3}/>
                  <stop offset="95%" stopColor="#06b6d4" stopOpacity={0}/>
                </linearGradient>
              </defs>
              <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
              <XAxis 
                dataKey="date" 
                stroke="#64748b"
                fontSize={12}
              />
              <YAxis 
                yAxisId="value"
                orientation="left"
                stroke="#64748b"
                fontSize={12}
                tickFormatter={formatCurrency}
              />
              <YAxis 
                yAxisId="items"
                orientation="right"
                stroke="#64748b"
                fontSize={12}
              />
              <Tooltip content={<CustomTooltip />} />
              <Area
                yAxisId="value"
                type="monotone"
                dataKey="value"
                stroke="#3b82f6"
                strokeWidth={2}
                fill="url(#valueGradient)"
                name="Inventory Value"
              />
              <Line
                yAxisId="items"
                type="monotone"
                dataKey="items"
                stroke="#06b6d4"
                strokeWidth={2}
                dot={{ fill: '#06b6d4', strokeWidth: 2, r: 4 }}
                name="Item Count"
              />
            </AreaChart>
          ) : (
            <LineChart data={movementData}>
              <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
              <XAxis 
                dataKey="date" 
                stroke="#64748b"
                fontSize={12}
              />
              <YAxis 
                stroke="#64748b"
                fontSize={12}
              />
              <Tooltip content={<CustomTooltip />} />
              <Line
                type="monotone"
                dataKey="inbound"
                stroke="#22c55e"
                strokeWidth={2}
                dot={{ fill: '#22c55e', strokeWidth: 2, r: 4 }}
                name="Inbound"
              />
              <Line
                type="monotone"
                dataKey="outbound"
                stroke="#ef4444"
                strokeWidth={2}
                dot={{ fill: '#ef4444', strokeWidth: 2, r: 4 }}
                name="Outbound"
              />
              <Line
                type="monotone"
                dataKey="net"
                stroke="#3b82f6"
                strokeWidth={2}
                dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}
                name="Net Change"
              />
            </LineChart>
          )}
        </ResponsiveContainer>
      </div>
    </Card>
  )
}

export default DashboardChart
