import React from 'react'
import { motion } from 'framer-motion'
import { 
  <PERSON>u, 
  Bell, 
  Search, 
  User, 
  LogOut, 
  Settings,
  ChevronDown
} from 'lucide-react'
import { useAuthStore } from '@store/authStore'
import { useNotificationStore } from '@store/notificationStore'
import Button from '@components/ui/Button'
import Input from '@components/ui/Input'
import { clsx } from 'clsx'

const Header = ({ onMenuClick, onNotificationClick }) => {
  const { user, logout } = useAuthStore()
  const { unreadCount } = useNotificationStore()
  const [userMenuOpen, setUserMenuOpen] = React.useState(false)
  const [searchQuery, setSearchQuery] = React.useState('')
  
  const handleLogout = async () => {
    await logout()
    setUserMenuOpen(false)
  }
  
  const handleSearch = (e) => {
    e.preventDefault()
    // Implement global search functionality
    console.log('Searching for:', searchQuery)
  }
  
  return (
    <header className="bg-white border-b border-neutral-200 shadow-sm">
      <div className="flex items-center justify-between h-16 px-4 sm:px-6 lg:px-8">
        {/* Left section */}
        <div className="flex items-center space-x-4">
          {/* Mobile menu button */}
          <button
            onClick={onMenuClick}
            className="lg:hidden p-2 rounded-lg hover:bg-neutral-100 transition-colors"
          >
            <Menu className="w-5 h-5 text-neutral-600" />
          </button>
          
          {/* Search */}
          <form onSubmit={handleSearch} className="hidden sm:block">
            <div className="relative">
              <Input
                type="text"
                placeholder="Search inventory, products, suppliers..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                icon={<Search className="w-4 h-4" />}
                className="w-80 pl-10"
              />
            </div>
          </form>
        </div>
        
        {/* Right section */}
        <div className="flex items-center space-x-4">
          {/* Mobile search button */}
          <button className="sm:hidden p-2 rounded-lg hover:bg-neutral-100 transition-colors">
            <Search className="w-5 h-5 text-neutral-600" />
          </button>
          
          {/* Notifications */}
          <div className="relative">
            <button
              onClick={onNotificationClick}
              className="relative p-2 rounded-lg hover:bg-neutral-100 transition-colors"
            >
              <Bell className="w-5 h-5 text-neutral-600" />
              {unreadCount > 0 && (
                <motion.span
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  className="absolute -top-1 -right-1 flex items-center justify-center w-5 h-5 text-xs font-medium text-white bg-danger-500 rounded-full"
                >
                  {unreadCount > 99 ? '99+' : unreadCount}
                </motion.span>
              )}
            </button>
          </div>
          
          {/* User menu */}
          <div className="relative">
            <button
              onClick={() => setUserMenuOpen(!userMenuOpen)}
              className="flex items-center space-x-3 p-2 rounded-lg hover:bg-neutral-100 transition-colors"
            >
              <div className="flex items-center justify-center w-8 h-8 bg-primary-100 rounded-full">
                <span className="text-sm font-medium text-primary-700">
                  {user?.firstName?.[0]}{user?.lastName?.[0]}
                </span>
              </div>
              <div className="hidden md:block text-left">
                <p className="text-sm font-medium text-neutral-900">
                  {user?.firstName} {user?.lastName}
                </p>
                <p className="text-xs text-neutral-500 capitalize">
                  {user?.role}
                </p>
              </div>
              <ChevronDown className="w-4 h-4 text-neutral-400" />
            </button>
            
            {/* User dropdown menu */}
            {userMenuOpen && (
              <motion.div
                initial={{ opacity: 0, scale: 0.95, y: -10 }}
                animate={{ opacity: 1, scale: 1, y: 0 }}
                exit={{ opacity: 0, scale: 0.95, y: -10 }}
                className="absolute right-0 mt-2 w-56 bg-white rounded-lg shadow-strong border border-neutral-200 py-2 z-50"
              >
                <div className="px-4 py-3 border-b border-neutral-200">
                  <p className="text-sm font-medium text-neutral-900">
                    {user?.firstName} {user?.lastName}
                  </p>
                  <p className="text-sm text-neutral-500">{user?.email}</p>
                </div>
                
                <div className="py-2">
                  <button
                    onClick={() => setUserMenuOpen(false)}
                    className="flex items-center w-full px-4 py-2 text-sm text-neutral-700 hover:bg-neutral-100 transition-colors"
                  >
                    <User className="w-4 h-4 mr-3" />
                    Profile
                  </button>
                  
                  <button
                    onClick={() => setUserMenuOpen(false)}
                    className="flex items-center w-full px-4 py-2 text-sm text-neutral-700 hover:bg-neutral-100 transition-colors"
                  >
                    <Settings className="w-4 h-4 mr-3" />
                    Settings
                  </button>
                </div>
                
                <div className="border-t border-neutral-200 py-2">
                  <button
                    onClick={handleLogout}
                    className="flex items-center w-full px-4 py-2 text-sm text-danger-600 hover:bg-danger-50 transition-colors"
                  >
                    <LogOut className="w-4 h-4 mr-3" />
                    Sign out
                  </button>
                </div>
              </motion.div>
            )}
          </div>
        </div>
      </div>
      
      {/* Mobile search bar */}
      <div className="sm:hidden border-t border-neutral-200 p-4">
        <form onSubmit={handleSearch}>
          <Input
            type="text"
            placeholder="Search..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            icon={<Search className="w-4 h-4" />}
            fullWidth
          />
        </form>
      </div>
    </header>
  )
}

export default Header
