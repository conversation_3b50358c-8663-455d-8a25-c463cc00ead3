import React from 'react'
import { clsx } from 'clsx'
import { Eye, EyeOff, AlertCircle } from 'lucide-react'

const Input = React.forwardRef(({
  label,
  error,
  helperText,
  type = 'text',
  size = 'md',
  fullWidth = false,
  disabled = false,
  required = false,
  placeholder,
  icon,
  iconPosition = 'left',
  className,
  containerClassName,
  ...props
}, ref) => {
  const [showPassword, setShowPassword] = React.useState(false)
  const [focused, setFocused] = React.useState(false)
  
  const inputId = React.useId()
  const isPassword = type === 'password'
  const inputType = isPassword && showPassword ? 'text' : type
  
  const baseClasses = 'block w-full rounded-lg border transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-0 disabled:opacity-50 disabled:cursor-not-allowed'
  
  const sizes = {
    sm: 'px-3 py-2 text-sm',
    md: 'px-3 py-2.5 text-sm',
    lg: 'px-4 py-3 text-base'
  }
  
  const variants = {
    default: clsx(
      'border-neutral-300 bg-white text-neutral-900 placeholder-neutral-500',
      'focus:border-primary-500 focus:ring-primary-500',
      error && 'border-danger-300 focus:border-danger-500 focus:ring-danger-500'
    ),
    filled: clsx(
      'border-transparent bg-neutral-100 text-neutral-900 placeholder-neutral-500',
      'focus:bg-white focus:border-primary-500 focus:ring-primary-500',
      error && 'bg-danger-50 focus:border-danger-500 focus:ring-danger-500'
    )
  }
  
  const iconClasses = 'w-5 h-5 text-neutral-400'
  
  const inputClasses = clsx(
    baseClasses,
    sizes[size],
    variants.default,
    icon && iconPosition === 'left' && 'pl-10',
    icon && iconPosition === 'right' && 'pr-10',
    isPassword && 'pr-10',
    fullWidth && 'w-full',
    className
  )
  
  const containerClasses = clsx(
    'relative',
    fullWidth && 'w-full',
    containerClassName
  )
  
  return (
    <div className={containerClasses}>
      {label && (
        <label 
          htmlFor={inputId}
          className={clsx(
            'block text-sm font-medium mb-2',
            error ? 'text-danger-700' : 'text-neutral-700',
            disabled && 'opacity-50'
          )}
        >
          {label}
          {required && <span className="text-danger-500 ml-1">*</span>}
        </label>
      )}
      
      <div className="relative">
        {icon && iconPosition === 'left' && (
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <span className={iconClasses}>{icon}</span>
          </div>
        )}
        
        <input
          ref={ref}
          id={inputId}
          type={inputType}
          disabled={disabled}
          placeholder={placeholder}
          className={inputClasses}
          onFocus={() => setFocused(true)}
          onBlur={() => setFocused(false)}
          {...props}
        />
        
        {icon && iconPosition === 'right' && !isPassword && (
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
            <span className={iconClasses}>{icon}</span>
          </div>
        )}
        
        {isPassword && (
          <button
            type="button"
            className="absolute inset-y-0 right-0 pr-3 flex items-center"
            onClick={() => setShowPassword(!showPassword)}
          >
            {showPassword ? (
              <EyeOff className={iconClasses} />
            ) : (
              <Eye className={iconClasses} />
            )}
          </button>
        )}
      </div>
      
      {error && (
        <div className="mt-2 flex items-center text-sm text-danger-600">
          <AlertCircle className="w-4 h-4 mr-1 flex-shrink-0" />
          <span>{error}</span>
        </div>
      )}
      
      {helperText && !error && (
        <p className="mt-2 text-sm text-neutral-500">{helperText}</p>
      )}
    </div>
  )
})

Input.displayName = 'Input'

export default Input
