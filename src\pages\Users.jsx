import React from 'react'
import { motion } from 'framer-motion'
import { Users as UsersIcon, Plus, Search, Filter } from 'lucide-react'
import Card from '@components/ui/Card'
import Button from '@components/ui/Button'
import Input from '@components/ui/Input'

const Users = () => {
  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-between"
      >
        <div>
          <h1 className="text-3xl font-bold text-neutral-900 mb-2">
            User Management
          </h1>
          <p className="text-neutral-600">
            Manage user accounts, roles, and permissions
          </p>
        </div>
        
        <Button
          icon={<Plus className="w-4 h-4" />}
          className="bg-gradient-primary"
        >
          Add User
        </Button>
      </motion.div>
      
      {/* Search and Filters */}
      <Card>
        <div className="flex items-center space-x-4">
          <div className="flex-1">
            <Input
              placeholder="Search users by name, email, or role..."
              icon={<Search className="w-4 h-4" />}
            />
          </div>
          <Button variant="outline" icon={<Filter className="w-4 h-4" />}>
            Filters
          </Button>
        </div>
      </Card>
      
      {/* Coming Soon */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <Card className="text-center py-16">
          <UsersIcon className="w-16 h-16 text-primary-600 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-neutral-900 mb-2">
            User Management Coming Soon
          </h3>
          <p className="text-neutral-600 mb-6 max-w-md mx-auto">
            This section will include comprehensive user management with role-based 
            access control, permissions, and user activity tracking.
          </p>
          <div className="flex justify-center space-x-4">
            <Button variant="outline">View Demo Data</Button>
            <Button>Import Users</Button>
          </div>
        </Card>
      </motion.div>
    </div>
  )
}

export default Users
