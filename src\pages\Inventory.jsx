import React from 'react'
import { motion } from 'framer-motion'
import { Warehouse, Plus, Search, Filter, Download } from 'lucide-react'
import Card from '@components/ui/Card'
import Button from '@components/ui/Button'
import Input from '@components/ui/Input'

const Inventory = () => {
  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-between"
      >
        <div>
          <h1 className="text-3xl font-bold text-neutral-900 mb-2">
            Inventory Management
          </h1>
          <p className="text-neutral-600">
            Track stock levels, movements, and manage inventory across locations
          </p>
        </div>
        
        <div className="flex items-center space-x-3">
          <Button variant="outline" icon={<Download className="w-4 h-4" />}>
            Export
          </Button>
          <Button
            icon={<Plus className="w-4 h-4" />}
            className="bg-gradient-primary"
          >
            Add Stock
          </Button>
        </div>
      </motion.div>
      
      {/* Search and Filters */}
      <Card>
        <div className="flex items-center space-x-4">
          <div className="flex-1">
            <Input
              placeholder="Search inventory by product, SKU, or location..."
              icon={<Search className="w-4 h-4" />}
            />
          </div>
          <Button variant="outline" icon={<Filter className="w-4 h-4" />}>
            Filters
          </Button>
        </div>
      </Card>
      
      {/* Coming Soon */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <Card className="text-center py-16">
          <Warehouse className="w-16 h-16 text-primary-600 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-neutral-900 mb-2">
            Inventory Management Coming Soon
          </h3>
          <p className="text-neutral-600 mb-6 max-w-md mx-auto">
            This section will include real-time inventory tracking, stock level monitoring, 
            automated reorder points, and inventory movement history.
          </p>
          <div className="flex justify-center space-x-4">
            <Button variant="outline">View Demo Data</Button>
            <Button>Import Inventory</Button>
          </div>
        </Card>
      </motion.div>
    </div>
  )
}

export default Inventory
