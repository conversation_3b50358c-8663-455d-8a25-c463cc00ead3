import React from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  X, 
  Bell, 
  CheckCircle, 
  AlertTriangle, 
  Info, 
  Trash2,
  MarkAsRead
} from 'lucide-react'
import { useNotificationStore } from '@store/notificationStore'
import { NOTIFICATION_TYPES, NOTIFICATION_PRIORITY } from '@types'
import Button from '@components/ui/Button'
import { StatusBadge, PriorityBadge } from '@components/ui/Badge'
import { formatDistanceToNow } from 'date-fns'
import { clsx } from 'clsx'

const NotificationPanel = ({ open, onClose }) => {
  const {
    notifications,
    unreadCount,
    isLoading,
    fetchNotifications,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    clearAllNotifications
  } = useNotificationStore()
  
  React.useEffect(() => {
    if (open) {
      fetchNotifications()
    }
  }, [open, fetchNotifications])
  
  const getNotificationIcon = (type, priority) => {
    const iconClass = clsx(
      'w-5 h-5',
      priority === NOTIFICATION_PRIORITY.CRITICAL && 'text-danger-600',
      priority === NOTIFICATION_PRIORITY.HIGH && 'text-warning-600',
      priority === NOTIFICATION_PRIORITY.MEDIUM && 'text-primary-600',
      priority === NOTIFICATION_PRIORITY.LOW && 'text-neutral-600'
    )
    
    switch (type) {
      case NOTIFICATION_TYPES.LOW_STOCK:
      case NOTIFICATION_TYPES.OUT_OF_STOCK:
        return <AlertTriangle className={iconClass} />
      case NOTIFICATION_TYPES.ORDER_UPDATE:
        return <CheckCircle className={iconClass} />
      case NOTIFICATION_TYPES.SYSTEM_ALERT:
        return <AlertTriangle className={iconClass} />
      default:
        return <Info className={iconClass} />
    }
  }
  
  const panelVariants = {
    open: {
      x: 0,
      transition: {
        type: 'spring',
        stiffness: 300,
        damping: 30
      }
    },
    closed: {
      x: '100%',
      transition: {
        type: 'spring',
        stiffness: 300,
        damping: 30
      }
    }
  }
  
  return (
    <AnimatePresence>
      {open && (
        <>
          {/* Overlay */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-40 bg-neutral-900/50 backdrop-blur-sm"
            onClick={onClose}
          />
          
          {/* Panel */}
          <motion.div
            initial="closed"
            animate="open"
            exit="closed"
            variants={panelVariants}
            className="fixed inset-y-0 right-0 z-50 w-96 bg-white shadow-strong border-l border-neutral-200 flex flex-col"
          >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-neutral-200">
              <div className="flex items-center space-x-3">
                <Bell className="w-5 h-5 text-primary-600" />
                <h2 className="text-lg font-semibold text-neutral-900">
                  Notifications
                </h2>
                {unreadCount > 0 && (
                  <span className="flex items-center justify-center w-6 h-6 text-xs font-medium text-white bg-danger-500 rounded-full">
                    {unreadCount}
                  </span>
                )}
              </div>
              
              <button
                onClick={onClose}
                className="p-2 rounded-lg hover:bg-neutral-100 transition-colors"
              >
                <X className="w-5 h-5 text-neutral-600" />
              </button>
            </div>
            
            {/* Actions */}
            {notifications.length > 0 && (
              <div className="flex items-center justify-between p-4 border-b border-neutral-200">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={markAllAsRead}
                  disabled={unreadCount === 0}
                  icon={<CheckCircle className="w-4 h-4" />}
                >
                  Mark all read
                </Button>
                
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearAllNotifications}
                  icon={<Trash2 className="w-4 h-4" />}
                  className="text-danger-600 hover:text-danger-700 hover:bg-danger-50"
                >
                  Clear all
                </Button>
              </div>
            )}
            
            {/* Notifications list */}
            <div className="flex-1 overflow-y-auto custom-scrollbar">
              {isLoading ? (
                <div className="flex items-center justify-center h-32">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600" />
                </div>
              ) : notifications.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-32 text-neutral-500">
                  <Bell className="w-8 h-8 mb-2" />
                  <p className="text-sm">No notifications</p>
                </div>
              ) : (
                <div className="space-y-1 p-2">
                  {notifications.map((notification) => (
                    <motion.div
                      key={notification.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      className={clsx(
                        'p-4 rounded-lg border transition-colors cursor-pointer',
                        notification.isRead
                          ? 'bg-white border-neutral-200 hover:bg-neutral-50'
                          : 'bg-primary-50 border-primary-200 hover:bg-primary-100'
                      )}
                      onClick={() => !notification.isRead && markAsRead(notification.id)}
                    >
                      <div className="flex items-start space-x-3">
                        <div className="flex-shrink-0 mt-0.5">
                          {getNotificationIcon(notification.type, notification.priority)}
                        </div>
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between mb-1">
                            <p className={clsx(
                              'text-sm font-medium truncate',
                              notification.isRead ? 'text-neutral-700' : 'text-neutral-900'
                            )}>
                              {notification.title}
                            </p>
                            
                            <div className="flex items-center space-x-2 ml-2">
                              <PriorityBadge priority={notification.priority} size="xs" />
                              {!notification.isRead && (
                                <div className="w-2 h-2 bg-primary-600 rounded-full" />
                              )}
                            </div>
                          </div>
                          
                          <p className={clsx(
                            'text-sm mb-2',
                            notification.isRead ? 'text-neutral-500' : 'text-neutral-700'
                          )}>
                            {notification.message}
                          </p>
                          
                          <div className="flex items-center justify-between">
                            <span className="text-xs text-neutral-400">
                              {formatDistanceToNow(new Date(notification.createdAt), { addSuffix: true })}
                            </span>
                            
                            <button
                              onClick={(e) => {
                                e.stopPropagation()
                                deleteNotification(notification.id)
                              }}
                              className="p-1 rounded hover:bg-neutral-200 transition-colors"
                            >
                              <Trash2 className="w-3 h-3 text-neutral-400 hover:text-danger-600" />
                            </button>
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              )}
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  )
}

export default NotificationPanel
