import React from 'react'
import { motion } from 'framer-motion'
import { clsx } from 'clsx'

const Badge = ({ 
  children, 
  variant = 'default',
  size = 'md',
  icon,
  iconPosition = 'left',
  removable = false,
  onRemove,
  className,
  animate = false,
  ...props 
}) => {
  const baseClasses = 'inline-flex items-center font-medium rounded-full'
  
  const variants = {
    default: 'bg-neutral-100 text-neutral-800',
    primary: 'bg-primary-100 text-primary-800',
    secondary: 'bg-secondary-100 text-secondary-800',
    success: 'bg-success-100 text-success-800',
    warning: 'bg-warning-100 text-warning-800',
    danger: 'bg-danger-100 text-danger-800',
    info: 'bg-blue-100 text-blue-800',
    outline: 'border border-neutral-300 text-neutral-700 bg-white',
    solid: 'bg-neutral-800 text-white'
  }
  
  const sizes = {
    xs: 'px-2 py-0.5 text-xs',
    sm: 'px-2.5 py-0.5 text-xs',
    md: 'px-2.5 py-1 text-sm',
    lg: 'px-3 py-1 text-sm',
    xl: 'px-3 py-1.5 text-base'
  }
  
  const iconSizes = {
    xs: 'w-3 h-3',
    sm: 'w-3 h-3',
    md: 'w-4 h-4',
    lg: 'w-4 h-4',
    xl: 'w-5 h-5'
  }
  
  const classes = clsx(
    baseClasses,
    variants[variant],
    sizes[size],
    className
  )
  
  const iconClass = clsx(
    iconSizes[size],
    children && iconPosition === 'left' && 'mr-1',
    children && iconPosition === 'right' && 'ml-1'
  )
  
  const removeIconClass = clsx(
    iconSizes[size],
    'ml-1 cursor-pointer hover:bg-black/10 rounded-full p-0.5 transition-colors'
  )
  
  const badgeContent = (
    <>
      {icon && iconPosition === 'left' && (
        <span className={iconClass}>{icon}</span>
      )}
      {children && <span>{children}</span>}
      {icon && iconPosition === 'right' && (
        <span className={iconClass}>{icon}</span>
      )}
      {removable && (
        <button
          type="button"
          onClick={onRemove}
          className={removeIconClass}
          aria-label="Remove badge"
        >
          <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
            <path
              fillRule="evenodd"
              d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
              clipRule="evenodd"
            />
          </svg>
        </button>
      )}
    </>
  )
  
  if (animate) {
    return (
      <motion.span
        className={classes}
        initial={{ scale: 0, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0, opacity: 0 }}
        transition={{ type: 'spring', stiffness: 500, damping: 30 }}
        {...props}
      >
        {badgeContent}
      </motion.span>
    )
  }
  
  return (
    <span className={classes} {...props}>
      {badgeContent}
    </span>
  )
}

// Status Badge Component
export const StatusBadge = ({ status, ...props }) => {
  const statusConfig = {
    active: { variant: 'success', children: 'Active' },
    inactive: { variant: 'neutral', children: 'Inactive' },
    pending: { variant: 'warning', children: 'Pending' },
    suspended: { variant: 'danger', children: 'Suspended' },
    in_stock: { variant: 'success', children: 'In Stock' },
    low_stock: { variant: 'warning', children: 'Low Stock' },
    out_of_stock: { variant: 'danger', children: 'Out of Stock' },
    reserved: { variant: 'info', children: 'Reserved' },
    damaged: { variant: 'danger', children: 'Damaged' },
    confirmed: { variant: 'success', children: 'Confirmed' },
    shipped: { variant: 'info', children: 'Shipped' },
    delivered: { variant: 'success', children: 'Delivered' },
    cancelled: { variant: 'danger', children: 'Cancelled' },
    returned: { variant: 'warning', children: 'Returned' }
  }
  
  const config = statusConfig[status] || { variant: 'default', children: status }
  
  return <Badge {...config} {...props} />
}

// Priority Badge Component
export const PriorityBadge = ({ priority, ...props }) => {
  const priorityConfig = {
    low: { variant: 'default', children: 'Low' },
    medium: { variant: 'warning', children: 'Medium' },
    high: { variant: 'danger', children: 'High' },
    urgent: { variant: 'danger', children: 'Urgent' },
    critical: { variant: 'danger', children: 'Critical' }
  }
  
  const config = priorityConfig[priority] || { variant: 'default', children: priority }
  
  return <Badge {...config} {...props} />
}

export default Badge
