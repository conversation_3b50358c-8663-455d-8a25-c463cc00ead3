import React from 'react'
import { motion } from 'framer-motion'
import {
  Package,
  TrendingUp,
  AlertTriangle,
  DollarSign,
  Users,
  Building2,
  Activity,
  ArrowUpRight,
  ArrowDownRight
} from 'lucide-react'
import Card from '@components/ui/Card'
import { StatusBadge } from '@components/ui/Badge'
import LoadingSpinner from '@components/ui/LoadingSpinner'
import { useInventoryStore } from '@store/inventoryStore'
import { useAuthStore } from '@store/authStore'
import DashboardChart from '@components/dashboard/DashboardChart'
import InventoryOverview from '@components/dashboard/InventoryOverview'
import RecentActivity from '@components/dashboard/RecentActivity'
import LowStockAlerts from '@components/dashboard/LowStockAlerts'

const Dashboard = () => {
  const { user } = useAuthStore()
  const { isLoading } = useInventoryStore()
  
  // Mock data for demonstration
  const stats = [
    {
      title: 'Total Inventory Value',
      value: '$2,847,392',
      change: '+12.5%',
      changeType: 'increase',
      icon: DollarSign,
      color: 'success'
    },
    {
      title: 'Total Products',
      value: '1,247',
      change: '+23',
      changeType: 'increase',
      icon: Package,
      color: 'primary'
    },
    {
      title: 'Low Stock Items',
      value: '18',
      change: '-5',
      changeType: 'decrease',
      icon: AlertTriangle,
      color: 'warning'
    },
    {
      title: 'Active Suppliers',
      value: '42',
      change: '+2',
      changeType: 'increase',
      icon: Building2,
      color: 'info'
    }
  ]
  
  const recentOrders = [
    {
      id: 'ORD-001',
      supplier: 'Nokia Networks',
      items: 15,
      value: '$45,230',
      status: 'delivered',
      date: '2024-01-15'
    },
    {
      id: 'ORD-002',
      supplier: 'Ericsson',
      items: 8,
      value: '$23,450',
      status: 'shipped',
      date: '2024-01-14'
    },
    {
      id: 'ORD-003',
      supplier: 'Huawei',
      items: 22,
      value: '$67,890',
      status: 'confirmed',
      date: '2024-01-13'
    }
  ]
  
  const lowStockItems = [
    {
      id: 1,
      name: '5G Base Station Antenna',
      sku: 'ANT-5G-001',
      currentStock: 3,
      reorderPoint: 10,
      category: 'Antennas'
    },
    {
      id: 2,
      name: 'Fiber Optic Cable (100m)',
      sku: 'FOC-100M-001',
      currentStock: 5,
      reorderPoint: 15,
      category: 'Cables'
    },
    {
      id: 3,
      name: 'Network Switch 48-Port',
      sku: 'NSW-48P-001',
      currentStock: 2,
      reorderPoint: 8,
      category: 'Network Equipment'
    }
  ]
  
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" text="Loading dashboard..." />
      </div>
    )
  }
  
  return (
    <div className="space-y-6">
      {/* Welcome Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="mb-8"
      >
        <h1 className="text-3xl font-bold text-neutral-900 mb-2">
          Welcome back, {user?.firstName}! 👋
        </h1>
        <p className="text-neutral-600">
          Here's what's happening with your telecom inventory today.
        </p>
      </motion.div>
      
      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => {
          const Icon = stat.icon
          return (
            <motion.div
              key={stat.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card hover="lift" className="relative overflow-hidden">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-neutral-600 mb-1">
                      {stat.title}
                    </p>
                    <p className="text-2xl font-bold text-neutral-900">
                      {stat.value}
                    </p>
                    <div className="flex items-center mt-2">
                      {stat.changeType === 'increase' ? (
                        <ArrowUpRight className="w-4 h-4 text-success-600 mr-1" />
                      ) : (
                        <ArrowDownRight className="w-4 h-4 text-danger-600 mr-1" />
                      )}
                      <span className={`text-sm font-medium ${
                        stat.changeType === 'increase' 
                          ? 'text-success-600' 
                          : 'text-danger-600'
                      }`}>
                        {stat.change}
                      </span>
                      <span className="text-sm text-neutral-500 ml-1">
                        vs last month
                      </span>
                    </div>
                  </div>
                  
                  <div className={`p-3 rounded-lg bg-${stat.color}-100`}>
                    <Icon className={`w-6 h-6 text-${stat.color}-600`} />
                  </div>
                </div>
                
                {/* Background decoration */}
                <div className="absolute top-0 right-0 -mt-4 -mr-4 w-24 h-24 bg-gradient-to-br from-neutral-100 to-transparent rounded-full opacity-50" />
              </Card>
            </motion.div>
          )
        })}
      </div>
      
      {/* Charts and Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.4 }}
        >
          <DashboardChart />
        </motion.div>
        
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.5 }}
        >
          <InventoryOverview />
        </motion.div>
      </div>
      
      {/* Recent Activity and Alerts */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="lg:col-span-2"
        >
          <Card>
            <Card.Header>
              <Card.Title>Recent Orders</Card.Title>
              <Card.Description>
                Latest purchase orders and their status
              </Card.Description>
            </Card.Header>
            
            <div className="space-y-4">
              {recentOrders.map((order) => (
                <div key={order.id} className="flex items-center justify-between p-4 bg-neutral-50 rounded-lg">
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center justify-center w-10 h-10 bg-primary-100 rounded-lg">
                      <Package className="w-5 h-5 text-primary-600" />
                    </div>
                    <div>
                      <p className="font-medium text-neutral-900">{order.id}</p>
                      <p className="text-sm text-neutral-600">{order.supplier}</p>
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <p className="font-medium text-neutral-900">{order.value}</p>
                    <StatusBadge status={order.status} size="sm" />
                  </div>
                </div>
              ))}
            </div>
          </Card>
        </motion.div>
        
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7 }}
        >
          <LowStockAlerts items={lowStockItems} />
        </motion.div>
      </div>
    </div>
  )
}

export default Dashboard
