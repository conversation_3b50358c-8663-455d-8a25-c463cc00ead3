{"name": "telecom-inventory-system", "private": true, "version": "1.0.0", "type": "module", "description": "Comprehensive telecom inventory management system with real-time tracking and analytics", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "react-hook-form": "^7.48.2", "react-query": "^3.39.3", "axios": "^1.6.2", "socket.io-client": "^4.7.4", "recharts": "^2.8.0", "lucide-react": "^0.294.0", "framer-motion": "^10.16.16", "react-hot-toast": "^2.4.1", "date-fns": "^2.30.0", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0", "zustand": "^4.4.7", "react-table": "^7.8.0", "react-select": "^5.8.0", "react-datepicker": "^4.24.0"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@vitejs/plugin-react": "^4.1.1", "vite": "^5.0.0", "eslint": "^8.53.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "vitest": "^1.0.0", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.5", "@testing-library/user-event": "^14.5.1"}, "keywords": ["telecom", "inventory", "management", "react", "real-time", "dashboard"], "author": "Telecom Inventory Team", "license": "MIT"}