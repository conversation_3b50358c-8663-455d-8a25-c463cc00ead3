import React from 'react'
import { motion } from 'framer-motion'
import { FileText, Download, Calendar, Filter } from 'lucide-react'
import Card from '@components/ui/Card'
import Button from '@components/ui/Button'

const Reports = () => {
  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-between"
      >
        <div>
          <h1 className="text-3xl font-bold text-neutral-900 mb-2">
            Reports & Analytics
          </h1>
          <p className="text-neutral-600">
            Generate comprehensive reports and analyze inventory performance
          </p>
        </div>
        
        <div className="flex items-center space-x-3">
          <Button variant="outline" icon={<Calendar className="w-4 h-4" />}>
            Date Range
          </Button>
          <Button
            icon={<Download className="w-4 h-4" />}
            className="bg-gradient-primary"
          >
            Export Report
          </Button>
        </div>
      </motion.div>
      
      {/* Coming Soon */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <Card className="text-center py-16">
          <FileText className="w-16 h-16 text-primary-600 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-neutral-900 mb-2">
            Reports & Analytics Coming Soon
          </h3>
          <p className="text-neutral-600 mb-6 max-w-md mx-auto">
            This section will include comprehensive reporting with inventory summaries, 
            supplier performance, user activity, and financial analytics.
          </p>
          <div className="flex justify-center space-x-4">
            <Button variant="outline">View Sample Reports</Button>
            <Button>Generate Report</Button>
          </div>
        </Card>
      </motion.div>
    </div>
  )
}

export default Reports
