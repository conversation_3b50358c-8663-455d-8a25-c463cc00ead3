import React from 'react'
import { motion } from 'framer-motion'
import { clsx } from 'clsx'
import { Loader2 } from 'lucide-react'

const LoadingSpinner = ({ 
  size = 'md', 
  color = 'primary', 
  text,
  className,
  fullScreen = false,
  overlay = false
}) => {
  const sizes = {
    xs: 'w-4 h-4',
    sm: 'w-5 h-5',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
    xl: 'w-12 h-12'
  }
  
  const colors = {
    primary: 'text-primary-600',
    secondary: 'text-secondary-600',
    neutral: 'text-neutral-600',
    white: 'text-white'
  }
  
  const textSizes = {
    xs: 'text-xs',
    sm: 'text-sm',
    md: 'text-sm',
    lg: 'text-base',
    xl: 'text-lg'
  }
  
  const spinnerClasses = clsx(
    sizes[size],
    colors[color],
    'animate-spin',
    className
  )
  
  const containerClasses = clsx(
    'flex flex-col items-center justify-center',
    fullScreen && 'fixed inset-0 z-50',
    overlay && 'bg-white/80 backdrop-blur-sm'
  )
  
  const spinner = (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className={containerClasses}
    >
      <Loader2 className={spinnerClasses} />
      {text && (
        <p className={clsx(
          'mt-2 font-medium',
          colors[color],
          textSizes[size]
        )}>
          {text}
        </p>
      )}
    </motion.div>
  )
  
  return spinner
}

// Skeleton Loading Component
export const Skeleton = ({ 
  className, 
  width, 
  height, 
  rounded = true,
  animate = true 
}) => {
  const classes = clsx(
    'bg-neutral-200',
    animate && 'animate-pulse',
    rounded && 'rounded',
    className
  )
  
  const style = {
    width,
    height
  }
  
  return <div className={classes} style={style} />
}

// Loading Dots Component
export const LoadingDots = ({ 
  size = 'md', 
  color = 'primary',
  className 
}) => {
  const sizes = {
    sm: 'w-1 h-1',
    md: 'w-2 h-2',
    lg: 'w-3 h-3'
  }
  
  const colors = {
    primary: 'bg-primary-600',
    secondary: 'bg-secondary-600',
    neutral: 'bg-neutral-600'
  }
  
  const dotClasses = clsx(
    sizes[size],
    colors[color],
    'rounded-full'
  )
  
  return (
    <div className={clsx('flex space-x-1', className)}>
      {[0, 1, 2].map((i) => (
        <motion.div
          key={i}
          className={dotClasses}
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.7, 1, 0.7]
          }}
          transition={{
            duration: 1,
            repeat: Infinity,
            delay: i * 0.2
          }}
        />
      ))}
    </div>
  )
}

// Loading Bar Component
export const LoadingBar = ({ 
  progress = 0, 
  color = 'primary',
  height = 'sm',
  className,
  showPercentage = false
}) => {
  const heights = {
    xs: 'h-1',
    sm: 'h-2',
    md: 'h-3',
    lg: 'h-4'
  }
  
  const colors = {
    primary: 'bg-primary-600',
    secondary: 'bg-secondary-600',
    success: 'bg-success-600',
    warning: 'bg-warning-600',
    danger: 'bg-danger-600'
  }
  
  return (
    <div className={clsx('w-full', className)}>
      <div className={clsx(
        'w-full bg-neutral-200 rounded-full overflow-hidden',
        heights[height]
      )}>
        <motion.div
          className={clsx(heights[height], colors[color], 'rounded-full')}
          initial={{ width: 0 }}
          animate={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
          transition={{ duration: 0.5, ease: 'easeOut' }}
        />
      </div>
      {showPercentage && (
        <div className="text-xs text-neutral-600 mt-1 text-center">
          {Math.round(progress)}%
        </div>
      )}
    </div>
  )
}

export default LoadingSpinner
