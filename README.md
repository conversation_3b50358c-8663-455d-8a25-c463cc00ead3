# TelecomIMS - Telecom Inventory Management System

A comprehensive, modern inventory management system specifically designed for telecom companies. Built with React, featuring real-time updates, role-based access control, and a beautiful, responsive interface.

## 🚀 Features

### Core Functionality
- **Real-time Inventory Tracking** - Live updates using WebSocket connections
- **Role-based Access Control** - Admin, Manager, and Staff roles with granular permissions
- **Product Catalog Management** - Detailed specifications for telecom equipment
- **Supplier Management** - Contact information, contracts, and performance tracking
- **Stock Level Monitoring** - Automated low-stock alerts and reorder points
- **Real-time Notifications** - System alerts and inventory change notifications

### Technical Features
- **Modern React Architecture** - Built with React 18, hooks, and functional components
- **State Management** - Zustand for efficient global state management
- **Real-time Updates** - WebSocket integration for live data synchronization
- **Responsive Design** - Works seamlessly on desktop, tablet, and mobile devices
- **Performance Optimized** - Code splitting, lazy loading, and optimized bundles

### UI/UX Features
- **Modern Design System** - Telecom-focused color scheme and typography
- **Smooth Animations** - Framer Motion for delightful user interactions
- **Accessibility** - WCAG compliant with keyboard navigation and screen reader support
- **Dark/Light Mode** - Adaptive theming (coming soon)

## 🛠️ Tech Stack

- **Frontend**: React 18, Vite, TypeScript
- **Styling**: Tailwind CSS, Framer Motion
- **State Management**: Zustand
- **Data Fetching**: React Query, Axios
- **Charts**: Recharts
- **Forms**: React Hook Form
- **Icons**: Lucide React
- **Testing**: Vitest, Testing Library

## 📦 Installation

### Prerequisites
- Node.js 18+ 
- npm or yarn

### Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd telecom-inventory-system
   ```

2. **Install dependencies**
   ```bash
   npm install
   # or
   yarn install
   ```

3. **Environment Configuration**
   ```bash
   cp .env.example .env
   ```
   
   Update the `.env` file with your configuration:
   ```env
   VITE_API_BASE_URL=http://localhost:3001/api
   VITE_WS_URL=ws://localhost:3001
   VITE_APP_NAME=TelecomIMS
   ```

4. **Start the development server**
   ```bash
   npm run dev
   # or
   yarn dev
   ```

5. **Open your browser**
   Navigate to `http://localhost:3000`

## 🔐 Demo Credentials

The application includes demo credentials for testing different user roles:

| Role | Email | Password |
|------|-------|----------|
| Admin | <EMAIL> | admin123 |
| Manager | <EMAIL> | manager123 |
| Staff | <EMAIL> | staff123 |

## 📁 Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── ui/             # Basic UI components (Button, Input, etc.)
│   ├── layout/         # Layout components (Sidebar, Header, etc.)
│   └── dashboard/      # Dashboard-specific components
├── pages/              # Page components
├── store/              # Zustand stores
├── services/           # API services
├── hooks/              # Custom React hooks
├── utils/              # Utility functions
├── types/              # Type definitions
└── assets/             # Static assets
```

## 🎨 Design System

The application uses a comprehensive design system with:

- **Color Palette**: Telecom-focused primary, secondary, and accent colors
- **Typography**: Inter font family with consistent sizing
- **Components**: Reusable UI components with consistent styling
- **Animations**: Smooth transitions and micro-interactions
- **Responsive Grid**: Mobile-first responsive design

## 🔧 Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint
- `npm test` - Run tests
- `npm run test:ui` - Run tests with UI

## 🚀 Deployment

### Build for Production

```bash
npm run build
```

The build artifacts will be stored in the `dist/` directory.

### Environment Variables

Make sure to set the following environment variables in your production environment:

- `VITE_API_BASE_URL` - Your API base URL
- `VITE_WS_URL` - Your WebSocket URL
- `VITE_APP_NAME` - Application name

## 📊 Features Overview

### Dashboard
- Real-time KPIs and analytics
- Inventory value tracking
- Low stock alerts
- Recent activity feed
- Interactive charts and graphs

### Product Catalog
- Comprehensive product management
- Telecom equipment specifications
- Category-based organization
- Bulk operations support

### Inventory Management
- Real-time stock tracking
- Multi-location support
- Stock movement history
- Automated reorder points

### Supplier Management
- Supplier contact information
- Contract tracking
- Performance metrics
- Relationship management

### User Management (Admin Only)
- Role-based access control
- User activity tracking
- Permission management
- Audit trails

### Reports & Analytics
- Inventory summaries
- Supplier performance reports
- User activity reports
- Financial analytics

## 🔮 Roadmap

- [ ] Advanced search and filtering
- [ ] Barcode scanning integration
- [ ] Mobile app (React Native)
- [ ] Advanced analytics and ML insights
- [ ] Integration with ERP systems
- [ ] Multi-language support
- [ ] Dark mode theme

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Contact the development team
- Check the documentation

## 🙏 Acknowledgments

- React team for the amazing framework
- Tailwind CSS for the utility-first CSS framework
- Framer Motion for smooth animations
- All the open-source contributors who made this project possible
