// Mock authentication service for demonstration
// In a real application, this would connect to your backend API

const DEMO_USERS = [
  {
    id: '1',
    email: '<EMAIL>',
    password: 'admin123',
    firstName: '<PERSON>',
    lastName: 'Admin',
    role: 'admin',
    permissions: ['all'],
    avatar: null,
    createdAt: '2024-01-01T00:00:00Z'
  },
  {
    id: '2',
    email: '<EMAIL>',
    password: 'manager123',
    firstName: '<PERSON>',
    lastName: 'Manager',
    role: 'manager',
    permissions: ['inventory', 'products', 'suppliers', 'reports'],
    avatar: null,
    createdAt: '2024-01-01T00:00:00Z'
  },
  {
    id: '3',
    email: '<EMAIL>',
    password: 'staff123',
    firstName: 'Bob',
    lastName: 'Staff',
    role: 'staff',
    permissions: ['inventory', 'products'],
    avatar: null,
    createdAt: '2024-01-01T00:00:00Z'
  }
]

// Simulate API delay
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms))

export const mockAuthService = {
  // Login user
  login: async (credentials) => {
    await delay(1000) // Simulate network delay
    
    const user = DEMO_USERS.find(u => 
      u.email === credentials.email && u.password === credentials.password
    )
    
    if (!user) {
      throw new Error('Invalid email or password')
    }
    
    const { password, ...userWithoutPassword } = user
    const token = `mock-token-${user.id}-${Date.now()}`
    
    return {
      data: {
        user: userWithoutPassword,
        token
      }
    }
  },
  
  // Logout user
  logout: async () => {
    await delay(500)
    return { data: { message: 'Logged out successfully' } }
  },
  
  // Get current user profile
  getCurrentUser: async () => {
    await delay(500)
    
    // In a real app, you'd validate the token here
    const token = localStorage.getItem('auth_token')
    if (!token) {
      throw new Error('No token found')
    }
    
    // Extract user ID from mock token
    const userId = token.split('-')[2]
    const user = DEMO_USERS.find(u => u.id === userId)
    
    if (!user) {
      throw new Error('User not found')
    }
    
    const { password, ...userWithoutPassword } = user
    return { data: userWithoutPassword }
  },
  
  // Update user profile
  updateProfile: async (profileData) => {
    await delay(1000)
    
    // In a real app, you'd update the user in the database
    const token = localStorage.getItem('auth_token')
    const userId = token.split('-')[2]
    const user = DEMO_USERS.find(u => u.id === userId)
    
    if (!user) {
      throw new Error('User not found')
    }
    
    // Update user data (in memory for demo)
    Object.assign(user, profileData)
    const { password, ...userWithoutPassword } = user
    
    return { data: userWithoutPassword }
  },
  
  // Change password
  changePassword: async (passwordData) => {
    await delay(1000)
    
    const { currentPassword, newPassword } = passwordData
    const token = localStorage.getItem('auth_token')
    const userId = token.split('-')[2]
    const user = DEMO_USERS.find(u => u.id === userId)
    
    if (!user) {
      throw new Error('User not found')
    }
    
    if (user.password !== currentPassword) {
      throw new Error('Current password is incorrect')
    }
    
    // Update password (in memory for demo)
    user.password = newPassword
    
    return { data: { message: 'Password changed successfully' } }
  }
}

// Override the real auth service with mock for demo
export const authService = mockAuthService
