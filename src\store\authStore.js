import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { authService } from '@services/mockAuthService'
import toast from 'react-hot-toast'

const useAuthStore = create(
  persist(
    (set, get) => ({
      // State
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,
      
      // Actions
      login: async (credentials) => {
        set({ isLoading: true, error: null })
        
        try {
          const response = await authService.login(credentials)
          const { user, token } = response.data
          
          // Store token in localStorage
          localStorage.setItem('auth_token', token)
          
          set({
            user,
            isAuthenticated: true,
            isLoading: false,
            error: null
          })
          
          toast.success(`Welcome back, ${user.firstName}!`)
          return { success: true }
        } catch (error) {
          const errorMessage = error.response?.data?.message || 'Login failed'
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: errorMessage
          })
          
          toast.error(errorMessage)
          return { success: false, error: errorMessage }
        }
      },
      
      logout: async () => {
        set({ isLoading: true })
        
        try {
          await authService.logout()
        } catch (error) {
          console.error('Logout error:', error)
        } finally {
          // Clear all auth data
          localStorage.removeItem('auth_token')
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: null
          })
          
          toast.success('Logged out successfully')
        }
      },
      
      register: async (userData) => {
        set({ isLoading: true, error: null })
        
        try {
          const response = await authService.register(userData)
          const { user, token } = response.data
          
          localStorage.setItem('auth_token', token)
          
          set({
            user,
            isAuthenticated: true,
            isLoading: false,
            error: null
          })
          
          toast.success('Account created successfully!')
          return { success: true }
        } catch (error) {
          const errorMessage = error.response?.data?.message || 'Registration failed'
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: errorMessage
          })
          
          toast.error(errorMessage)
          return { success: false, error: errorMessage }
        }
      },
      
      updateProfile: async (profileData) => {
        set({ isLoading: true, error: null })
        
        try {
          const response = await authService.updateProfile(profileData)
          const updatedUser = response.data
          
          set({
            user: updatedUser,
            isLoading: false,
            error: null
          })
          
          toast.success('Profile updated successfully!')
          return { success: true }
        } catch (error) {
          const errorMessage = error.response?.data?.message || 'Profile update failed'
          set({
            isLoading: false,
            error: errorMessage
          })
          
          toast.error(errorMessage)
          return { success: false, error: errorMessage }
        }
      },
      
      changePassword: async (passwordData) => {
        set({ isLoading: true, error: null })
        
        try {
          await authService.changePassword(passwordData)
          
          set({
            isLoading: false,
            error: null
          })
          
          toast.success('Password changed successfully!')
          return { success: true }
        } catch (error) {
          const errorMessage = error.response?.data?.message || 'Password change failed'
          set({
            isLoading: false,
            error: errorMessage
          })
          
          toast.error(errorMessage)
          return { success: false, error: errorMessage }
        }
      },
      
      initializeAuth: async () => {
        const token = localStorage.getItem('auth_token')
        
        if (!token) {
          set({ isLoading: false })
          return
        }
        
        set({ isLoading: true })
        
        try {
          const response = await authService.getCurrentUser()
          const user = response.data
          
          set({
            user,
            isAuthenticated: true,
            isLoading: false,
            error: null
          })
        } catch (error) {
          // Token is invalid, clear it
          localStorage.removeItem('auth_token')
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: null
          })
        }
      },
      
      clearError: () => {
        set({ error: null })
      },
      
      // Utility functions
      hasRole: (role) => {
        const { user } = get()
        return user?.role === role || user?.role === 'admin'
      },
      
      hasPermission: (permission) => {
        const { user } = get()
        return user?.permissions?.includes(permission) || user?.role === 'admin'
      }
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated
      })
    }
  )
)

export { useAuthStore }
