import { apiHelpers, endpoints } from './api'

export const inventoryService = {
  // Get inventory items with pagination and filters
  getInventoryItems: (params = {}) => {
    return apiHelpers.get(endpoints.inventory.list, params)
  },
  
  // Get single inventory item
  getInventoryItem: (id) => {
    return apiHelpers.get(endpoints.inventory.get(id))
  },
  
  // Update inventory item
  updateInventoryItem: (id, data) => {
    return apiHelpers.put(endpoints.inventory.update(id), data)
  },
  
  // Adjust stock levels
  adjustStock: (id, adjustment) => {
    return apiHelpers.post(endpoints.inventory.adjust(id), adjustment)
  },
  
  // Transfer stock between locations
  transferStock: (transferData) => {
    return apiHelpers.post(endpoints.inventory.transfer, transferData)
  },
  
  // Get inventory movements/history
  getMovements: (params = {}) => {
    return apiHelpers.get(endpoints.inventory.movements, params)
  },
  
  // Get all locations
  getLocations: () => {
    return apiHelpers.get(endpoints.inventory.locations)
  },
  
  // Get inventory reports
  getReports: (reportType, params = {}) => {
    return apiHelpers.get(`${endpoints.inventory.reports}/${reportType}`, params)
  },
  
  // Bulk operations
  bulkUpdate: (items) => {
    return apiHelpers.post(`${endpoints.inventory.list}/bulk`, { items })
  },
  
  // Export inventory data
  exportInventory: (format = 'csv', filters = {}) => {
    return apiHelpers.download(
      `${endpoints.inventory.list}/export?format=${format}`,
      `inventory_export.${format}`
    )
  },
  
  // Import inventory data
  importInventory: (file, onProgress = null) => {
    const formData = new FormData()
    formData.append('file', file)
    return apiHelpers.upload(`${endpoints.inventory.list}/import`, formData, onProgress)
  },
  
  // Get low stock items
  getLowStockItems: () => {
    return apiHelpers.get(`${endpoints.inventory.list}/low-stock`)
  },
  
  // Get out of stock items
  getOutOfStockItems: () => {
    return apiHelpers.get(`${endpoints.inventory.list}/out-of-stock`)
  },
  
  // Get inventory summary/statistics
  getInventorySummary: () => {
    return apiHelpers.get(`${endpoints.inventory.list}/summary`)
  },
  
  // Set reorder points
  setReorderPoint: (id, reorderPoint) => {
    return apiHelpers.patch(endpoints.inventory.update(id), { reorderPoint })
  },
  
  // Reserve inventory
  reserveInventory: (id, quantity, reason) => {
    return apiHelpers.post(`${endpoints.inventory.get(id)}/reserve`, {
      quantity,
      reason
    })
  },
  
  // Release reserved inventory
  releaseReservation: (id, reservationId) => {
    return apiHelpers.post(`${endpoints.inventory.get(id)}/release`, {
      reservationId
    })
  }
}
