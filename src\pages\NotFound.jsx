import React from 'react'
import { motion } from 'framer-motion'
import { useNavigate } from 'react-router-dom'
import { Home, ArrowLeft, Search } from 'lucide-react'
import Button from '@components/ui/Button'
import Card from '@components/ui/Card'

const NotFound = () => {
  const navigate = useNavigate()
  
  return (
    <div className="min-h-screen bg-neutral-50 flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center max-w-md mx-auto"
      >
        <Card className="p-8">
          {/* 404 Animation */}
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, type: 'spring', stiffness: 200 }}
            className="mb-6"
          >
            <div className="text-8xl font-bold text-primary-600 mb-4">404</div>
            <div className="w-24 h-1 bg-gradient-primary mx-auto rounded-full" />
          </motion.div>
          
          {/* Content */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.4 }}
          >
            <h1 className="text-2xl font-bold text-neutral-900 mb-2">
              Page Not Found
            </h1>
            <p className="text-neutral-600 mb-8">
              The page you're looking for doesn't exist or has been moved.
            </p>
          </motion.div>
          
          {/* Actions */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
            className="space-y-4"
          >
            <div className="flex flex-col sm:flex-row gap-3">
              <Button
                onClick={() => navigate('/dashboard')}
                icon={<Home className="w-4 h-4" />}
                className="bg-gradient-primary"
                fullWidth
              >
                Go to Dashboard
              </Button>
              
              <Button
                onClick={() => navigate(-1)}
                variant="outline"
                icon={<ArrowLeft className="w-4 h-4" />}
                fullWidth
              >
                Go Back
              </Button>
            </div>
            
            <div className="pt-4 border-t border-neutral-200">
              <p className="text-sm text-neutral-500 mb-3">
                Need help finding something?
              </p>
              <Button
                variant="ghost"
                icon={<Search className="w-4 h-4" />}
                size="sm"
              >
                Search Inventory
              </Button>
            </div>
          </motion.div>
        </Card>
        
        {/* Footer */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.8 }}
          className="mt-8 text-sm text-neutral-500"
        >
          If you believe this is an error, please contact support.
        </motion.div>
      </motion.div>
    </div>
  )
}

export default NotFound
