import React from 'react'
import { motion } from 'framer-motion'
import { AlertTriangle, Package, ArrowRight } from 'lucide-react'
import Card from '@components/ui/Card'
import Button from '@components/ui/Button'
import { StatusBadge } from '@components/ui/Badge'

const LowStockAlerts = ({ items = [] }) => {
  const getStockLevel = (current, reorder) => {
    const percentage = (current / reorder) * 100
    if (percentage <= 25) return { level: 'critical', color: 'danger' }
    if (percentage <= 50) return { level: 'low', color: 'warning' }
    return { level: 'normal', color: 'success' }
  }
  
  return (
    <Card>
      <Card.Header>
        <Card.Title className="flex items-center">
          <AlertTriangle className="w-5 h-5 mr-2 text-warning-600" />
          Low Stock Alerts
        </Card.Title>
        <Card.Description>
          Items that need immediate attention
        </Card.Description>
      </Card.Header>
      
      {items.length === 0 ? (
        <div className="flex flex-col items-center justify-center py-8 text-neutral-500">
          <Package className="w-8 h-8 mb-2" />
          <p className="text-sm">No low stock alerts</p>
        </div>
      ) : (
        <div className="space-y-4">
          {items.map((item, index) => {
            const stockInfo = getStockLevel(item.currentStock, item.reorderPoint)
            
            return (
              <motion.div
                key={item.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className="p-4 bg-neutral-50 rounded-lg border border-neutral-200 hover:border-warning-300 transition-colors"
              >
                <div className="flex items-start justify-between mb-3">
                  <div className="flex-1">
                    <h4 className="font-medium text-neutral-900 mb-1">
                      {item.name}
                    </h4>
                    <p className="text-sm text-neutral-600 mb-2">
                      SKU: {item.sku}
                    </p>
                    <StatusBadge 
                      status={item.category.toLowerCase().replace(' ', '_')} 
                      size="xs" 
                    />
                  </div>
                  
                  <div className="text-right">
                    <div className="flex items-center space-x-2 mb-1">
                      <span className="text-lg font-bold text-neutral-900">
                        {item.currentStock}
                      </span>
                      <span className="text-sm text-neutral-500">
                        / {item.reorderPoint}
                      </span>
                    </div>
                    <div className={`text-xs font-medium text-${stockInfo.color}-600`}>
                      {stockInfo.level.toUpperCase()}
                    </div>
                  </div>
                </div>
                
                {/* Stock level indicator */}
                <div className="mb-3">
                  <div className="flex justify-between text-xs text-neutral-600 mb-1">
                    <span>Current Stock</span>
                    <span>Reorder Point</span>
                  </div>
                  <div className="w-full bg-neutral-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full bg-${stockInfo.color}-500 transition-all duration-300`}
                      style={{ 
                        width: `${Math.min(100, (item.currentStock / item.reorderPoint) * 100)}%` 
                      }}
                    />
                  </div>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-xs text-neutral-500">
                    Category: {item.category}
                  </span>
                  
                  <Button
                    size="xs"
                    variant="outline"
                    icon={<ArrowRight className="w-3 h-3" />}
                    iconPosition="right"
                  >
                    Reorder
                  </Button>
                </div>
              </motion.div>
            )
          })}
          
          {/* View All Button */}
          <div className="pt-4 border-t border-neutral-200">
            <Button
              variant="ghost"
              fullWidth
              icon={<Package className="w-4 h-4" />}
            >
              View All Low Stock Items
            </Button>
          </div>
        </div>
      )}
    </Card>
  )
}

export default LowStockAlerts
