import axios from 'axios'
import toast from 'react-hot-toast'

// Create axios instance with default config
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    const { response } = error
    
    if (response) {
      switch (response.status) {
        case 401:
          // Unauthorized - clear token and redirect to login
          localStorage.removeItem('auth_token')
          window.location.href = '/login'
          toast.error('Session expired. Please login again.')
          break
          
        case 403:
          // Forbidden
          toast.error('You do not have permission to perform this action.')
          break
          
        case 404:
          // Not found
          toast.error('The requested resource was not found.')
          break
          
        case 422:
          // Validation error
          const validationErrors = response.data?.errors
          if (validationErrors && Array.isArray(validationErrors)) {
            validationErrors.forEach(err => toast.error(err.message))
          } else {
            toast.error(response.data?.message || 'Validation error occurred.')
          }
          break
          
        case 429:
          // Rate limit exceeded
          toast.error('Too many requests. Please try again later.')
          break
          
        case 500:
          // Server error
          toast.error('Internal server error. Please try again later.')
          break
          
        default:
          // Other errors
          toast.error(response.data?.message || 'An unexpected error occurred.')
      }
    } else if (error.request) {
      // Network error
      toast.error('Network error. Please check your connection.')
    } else {
      // Other error
      toast.error('An unexpected error occurred.')
    }
    
    return Promise.reject(error)
  }
)

// API endpoints
export const endpoints = {
  // Authentication
  auth: {
    login: '/auth/login',
    logout: '/auth/logout',
    register: '/auth/register',
    refresh: '/auth/refresh',
    profile: '/auth/profile',
    changePassword: '/auth/change-password',
    forgotPassword: '/auth/forgot-password',
    resetPassword: '/auth/reset-password'
  },
  
  // Users
  users: {
    list: '/users',
    create: '/users',
    get: (id) => `/users/${id}`,
    update: (id) => `/users/${id}`,
    delete: (id) => `/users/${id}`,
    roles: '/users/roles',
    permissions: '/users/permissions'
  },
  
  // Products
  products: {
    list: '/products',
    create: '/products',
    get: (id) => `/products/${id}`,
    update: (id) => `/products/${id}`,
    delete: (id) => `/products/${id}`,
    categories: '/products/categories',
    specifications: '/products/specifications',
    bulk: '/products/bulk'
  },
  
  // Inventory
  inventory: {
    list: '/inventory',
    get: (id) => `/inventory/${id}`,
    update: (id) => `/inventory/${id}`,
    adjust: (id) => `/inventory/${id}/adjust`,
    transfer: '/inventory/transfer',
    movements: '/inventory/movements',
    locations: '/inventory/locations',
    reports: '/inventory/reports'
  },
  
  // Suppliers
  suppliers: {
    list: '/suppliers',
    create: '/suppliers',
    get: (id) => `/suppliers/${id}`,
    update: (id) => `/suppliers/${id}`,
    delete: (id) => `/suppliers/${id}`,
    contracts: (id) => `/suppliers/${id}/contracts`,
    performance: (id) => `/suppliers/${id}/performance`
  },
  
  // Orders
  orders: {
    list: '/orders',
    create: '/orders',
    get: (id) => `/orders/${id}`,
    update: (id) => `/orders/${id}`,
    delete: (id) => `/orders/${id}`,
    status: (id) => `/orders/${id}/status`
  },
  
  // Notifications
  notifications: {
    list: '/notifications',
    create: '/notifications',
    markRead: (id) => `/notifications/${id}/read`,
    markAllRead: '/notifications/mark-all-read',
    delete: (id) => `/notifications/${id}`,
    clear: '/notifications/clear',
    settings: '/notifications/settings'
  },
  
  // Reports
  reports: {
    dashboard: '/reports/dashboard',
    inventory: '/reports/inventory',
    suppliers: '/reports/suppliers',
    users: '/reports/users',
    financial: '/reports/financial',
    export: '/reports/export'
  },
  
  // Audit
  audit: {
    list: '/audit',
    get: (id) => `/audit/${id}`,
    export: '/audit/export'
  },
  
  // Settings
  settings: {
    get: '/settings',
    update: '/settings',
    backup: '/settings/backup',
    restore: '/settings/restore'
  }
}

// Helper functions for common API operations
export const apiHelpers = {
  // GET request with query parameters
  get: (url, params = {}) => {
    return api.get(url, { params })
  },
  
  // POST request
  post: (url, data = {}) => {
    return api.post(url, data)
  },
  
  // PUT request
  put: (url, data = {}) => {
    return api.put(url, data)
  },
  
  // PATCH request
  patch: (url, data = {}) => {
    return api.patch(url, data)
  },
  
  // DELETE request
  delete: (url) => {
    return api.delete(url)
  },
  
  // Upload file
  upload: (url, formData, onProgress = null) => {
    return api.post(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      onUploadProgress: onProgress
    })
  },
  
  // Download file
  download: (url, filename) => {
    return api.get(url, {
      responseType: 'blob'
    }).then(response => {
      const blob = new Blob([response.data])
      const downloadUrl = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = downloadUrl
      link.download = filename
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(downloadUrl)
    })
  }
}

export default api
