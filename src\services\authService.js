import { apiHelpers, endpoints } from './api'

export const authService = {
  // Login user
  login: (credentials) => {
    return apiHelpers.post(endpoints.auth.login, credentials)
  },
  
  // Logout user
  logout: () => {
    return apiHelpers.post(endpoints.auth.logout)
  },
  
  // Register new user
  register: (userData) => {
    return apiHelpers.post(endpoints.auth.register, userData)
  },
  
  // Get current user profile
  getCurrentUser: () => {
    return apiHelpers.get(endpoints.auth.profile)
  },
  
  // Update user profile
  updateProfile: (profileData) => {
    return apiHelpers.put(endpoints.auth.profile, profileData)
  },
  
  // Change password
  changePassword: (passwordData) => {
    return apiHelpers.post(endpoints.auth.changePassword, passwordData)
  },
  
  // Forgot password
  forgotPassword: (email) => {
    return apiHelpers.post(endpoints.auth.forgotPassword, { email })
  },
  
  // Reset password
  resetPassword: (token, newPassword) => {
    return apiHelpers.post(endpoints.auth.resetPassword, { token, newPassword })
  },
  
  // Refresh token
  refreshToken: () => {
    return apiHelpers.post(endpoints.auth.refresh)
  },
  
  // Check if user is authenticated
  isAuthenticated: () => {
    const token = localStorage.getItem('auth_token')
    return !!token
  },
  
  // Get auth token
  getToken: () => {
    return localStorage.getItem('auth_token')
  },
  
  // Set auth token
  setToken: (token) => {
    localStorage.setItem('auth_token', token)
  },
  
  // Remove auth token
  removeToken: () => {
    localStorage.removeItem('auth_token')
  }
}
