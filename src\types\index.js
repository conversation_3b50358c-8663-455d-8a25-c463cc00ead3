// User Types
export const USER_ROLES = {
  ADMIN: 'admin',
  MANAGER: 'manager',
  STAFF: 'staff'
}

export const USER_STATUS = {
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  SUSPENDED: 'suspended'
}

// Product Types
export const PRODUCT_CATEGORIES = {
  NETWORK_EQUIPMENT: 'network_equipment',
  MOBILE_DEVICES: 'mobile_devices',
  ACCESSORIES: 'accessories',
  CABLES: 'cables',
  ANTENNAS: 'antennas',
  POWER_EQUIPMENT: 'power_equipment',
  TESTING_EQUIPMENT: 'testing_equipment',
  SOFTWARE_LICENSES: 'software_licenses'
}

export const PRODUCT_STATUS = {
  ACTIVE: 'active',
  DISCONTINUED: 'discontinued',
  PENDING: 'pending'
}

// Inventory Types
export const INVENTORY_STATUS = {
  IN_STOCK: 'in_stock',
  LOW_STOCK: 'low_stock',
  OUT_OF_STOCK: 'out_of_stock',
  RESERVED: 'reserved',
  DAMAGED: 'damaged'
}

export const MOVEMENT_TYPES = {
  STOCK_IN: 'stock_in',
  STOCK_OUT: 'stock_out',
  TRANSFER: 'transfer',
  ADJUSTMENT: 'adjustment',
  RETURN: 'return',
  DAMAGE: 'damage'
}

// Supplier Types
export const SUPPLIER_STATUS = {
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  BLACKLISTED: 'blacklisted'
}

export const SUPPLIER_RATING = {
  EXCELLENT: 5,
  GOOD: 4,
  AVERAGE: 3,
  POOR: 2,
  VERY_POOR: 1
}

// Order Types
export const ORDER_STATUS = {
  PENDING: 'pending',
  CONFIRMED: 'confirmed',
  SHIPPED: 'shipped',
  DELIVERED: 'delivered',
  CANCELLED: 'cancelled',
  RETURNED: 'returned'
}

export const ORDER_PRIORITY = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  URGENT: 'urgent'
}

// Notification Types
export const NOTIFICATION_TYPES = {
  LOW_STOCK: 'low_stock',
  OUT_OF_STOCK: 'out_of_stock',
  ORDER_UPDATE: 'order_update',
  SYSTEM_ALERT: 'system_alert',
  USER_ACTION: 'user_action',
  SUPPLIER_UPDATE: 'supplier_update'
}

export const NOTIFICATION_PRIORITY = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical'
}

// Audit Types
export const AUDIT_ACTIONS = {
  CREATE: 'create',
  UPDATE: 'update',
  DELETE: 'delete',
  LOGIN: 'login',
  LOGOUT: 'logout',
  STOCK_MOVEMENT: 'stock_movement',
  ORDER_PLACED: 'order_placed',
  ORDER_UPDATED: 'order_updated'
}

// Report Types
export const REPORT_TYPES = {
  INVENTORY_SUMMARY: 'inventory_summary',
  STOCK_MOVEMENT: 'stock_movement',
  SUPPLIER_PERFORMANCE: 'supplier_performance',
  USER_ACTIVITY: 'user_activity',
  FINANCIAL_SUMMARY: 'financial_summary',
  LOW_STOCK_REPORT: 'low_stock_report'
}

// Location Types
export const LOCATION_TYPES = {
  WAREHOUSE: 'warehouse',
  STORE: 'store',
  OFFICE: 'office',
  FIELD: 'field'
}

// Contract Types
export const CONTRACT_STATUS = {
  ACTIVE: 'active',
  EXPIRED: 'expired',
  PENDING: 'pending',
  TERMINATED: 'terminated'
}

// Equipment Specifications for Telecom
export const EQUIPMENT_SPECS = {
  FREQUENCY_BANDS: [
    '700MHz', '800MHz', '850MHz', '900MHz', '1800MHz', '1900MHz', 
    '2100MHz', '2300MHz', '2500MHz', '2600MHz', '3500MHz', '3700MHz'
  ],
  NETWORK_TYPES: [
    '2G', '3G', '4G LTE', '5G NR', 'WiFi 6', 'WiFi 6E', 'Bluetooth'
  ],
  POWER_RATINGS: [
    '1W', '2W', '5W', '10W', '20W', '50W', '100W', '200W', '500W'
  ],
  CONNECTOR_TYPES: [
    'SMA', 'N-Type', 'BNC', 'TNC', 'F-Type', 'RJ45', 'RJ11', 'USB-C'
  ]
}

// Default values and constants
export const DEFAULT_PAGINATION = {
  page: 1,
  limit: 20,
  total: 0
}

export const STOCK_THRESHOLDS = {
  LOW_STOCK: 10,
  CRITICAL_STOCK: 5,
  REORDER_POINT: 15
}

export const DATE_FORMATS = {
  DISPLAY: 'MMM dd, yyyy',
  INPUT: 'yyyy-MM-dd',
  DATETIME: 'MMM dd, yyyy HH:mm',
  TIME: 'HH:mm'
}
