import React from 'react'
import { Outlet } from 'react-router-dom'
import { motion, AnimatePresence } from 'framer-motion'
import Sidebar from './Sidebar'
import Header from './Header'
import NotificationPanel from './NotificationPanel'
import { useNotificationStore } from '@store/notificationStore'

const Layout = () => {
  const [sidebarOpen, setSidebarOpen] = React.useState(false)
  const [notificationPanelOpen, setNotificationPanelOpen] = React.useState(false)
  const { realTimeEnabled } = useNotificationStore()
  
  // Close sidebar on larger screens
  React.useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 1024) {
        setSidebarOpen(false)
      }
    }
    
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])
  
  return (
    <div className="min-h-screen bg-neutral-50">
      {/* Mobile sidebar overlay */}
      <AnimatePresence>
        {sidebarOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-40 lg:hidden"
            onClick={() => setSidebarOpen(false)}
          >
            <div className="absolute inset-0 bg-neutral-900/50 backdrop-blur-sm" />
          </motion.div>
        )}
      </AnimatePresence>
      
      {/* Sidebar */}
      <Sidebar 
        open={sidebarOpen} 
        onClose={() => setSidebarOpen(false)} 
      />
      
      {/* Main content area */}
      <div className="lg:pl-64">
        {/* Header */}
        <Header 
          onMenuClick={() => setSidebarOpen(true)}
          onNotificationClick={() => setNotificationPanelOpen(true)}
        />
        
        {/* Page content */}
        <main className="py-6 px-4 sm:px-6 lg:px-8">
          <div className="mx-auto max-w-7xl">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              <Outlet />
            </motion.div>
          </div>
        </main>
      </div>
      
      {/* Notification Panel */}
      <NotificationPanel 
        open={notificationPanelOpen}
        onClose={() => setNotificationPanelOpen(false)}
      />
      
      {/* Real-time connection indicator */}
      {realTimeEnabled && (
        <div className="fixed bottom-4 right-4 z-30">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            className="flex items-center space-x-2 bg-success-600 text-white px-3 py-2 rounded-full shadow-lg"
          >
            <div className="w-2 h-2 bg-white rounded-full animate-pulse" />
            <span className="text-sm font-medium">Live</span>
          </motion.div>
        </div>
      )}
    </div>
  )
}

export default Layout
