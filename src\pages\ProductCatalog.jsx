import React from 'react'
import { motion } from 'framer-motion'
import { Package, Plus, Search, Filter } from 'lucide-react'
import Card from '@components/ui/Card'
import Button from '@components/ui/Button'
import Input from '@components/ui/Input'

const ProductCatalog = () => {
  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-between"
      >
        <div>
          <h1 className="text-3xl font-bold text-neutral-900 mb-2">
            Product Catalog
          </h1>
          <p className="text-neutral-600">
            Manage your telecom equipment and product specifications
          </p>
        </div>
        
        <Button
          icon={<Plus className="w-4 h-4" />}
          className="bg-gradient-primary"
        >
          Add Product
        </Button>
      </motion.div>
      
      {/* Search and Filters */}
      <Card>
        <div className="flex items-center space-x-4">
          <div className="flex-1">
            <Input
              placeholder="Search products by name, SKU, or category..."
              icon={<Search className="w-4 h-4" />}
            />
          </div>
          <Button variant="outline" icon={<Filter className="w-4 h-4" />}>
            Filters
          </Button>
        </div>
      </Card>
      
      {/* Coming Soon */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <Card className="text-center py-16">
          <Package className="w-16 h-16 text-primary-600 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-neutral-900 mb-2">
            Product Catalog Coming Soon
          </h3>
          <p className="text-neutral-600 mb-6 max-w-md mx-auto">
            This section will include comprehensive product management with detailed 
            specifications for telecom equipment, categorization, and bulk operations.
          </p>
          <div className="flex justify-center space-x-4">
            <Button variant="outline">View Demo Data</Button>
            <Button>Import Products</Button>
          </div>
        </Card>
      </motion.div>
    </div>
  )
}

export default ProductCatalog
