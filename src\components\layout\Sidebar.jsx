import React from 'react'
import { NavLink, useLocation } from 'react-router-dom'
import { motion, AnimatePresence } from 'framer-motion'
import { clsx } from 'clsx'
import {
  LayoutDashboard,
  Package,
  Warehouse,
  Building2,
  Users,
  FileText,
  Settings,
  ChevronLeft,
  Signal,
  Zap
} from 'lucide-react'
import { useAuthStore } from '@store/authStore'

const Sidebar = ({ open, onClose }) => {
  const location = useLocation()
  const { user, hasRole } = useAuthStore()
  
  const navigation = [
    {
      name: 'Dashboard',
      href: '/dashboard',
      icon: LayoutDashboard,
      current: location.pathname === '/dashboard'
    },
    {
      name: 'Product Catalog',
      href: '/products',
      icon: Package,
      current: location.pathname === '/products'
    },
    {
      name: 'Inventory',
      href: '/inventory',
      icon: Warehouse,
      current: location.pathname === '/inventory'
    },
    {
      name: 'Suppliers',
      href: '/suppliers',
      icon: Building2,
      current: location.pathname === '/suppliers'
    },
    {
      name: 'Users',
      href: '/users',
      icon: Users,
      current: location.pathname === '/users',
      adminOnly: true
    },
    {
      name: 'Reports',
      href: '/reports',
      icon: FileText,
      current: location.pathname === '/reports'
    },
    {
      name: 'Settings',
      href: '/settings',
      icon: Settings,
      current: location.pathname === '/settings'
    }
  ]
  
  const filteredNavigation = navigation.filter(item => 
    !item.adminOnly || hasRole('admin')
  )
  
  const sidebarVariants = {
    open: {
      x: 0,
      transition: {
        type: 'spring',
        stiffness: 300,
        damping: 30
      }
    },
    closed: {
      x: '-100%',
      transition: {
        type: 'spring',
        stiffness: 300,
        damping: 30
      }
    }
  }
  
  return (
    <AnimatePresence>
      <motion.div
        initial="closed"
        animate={open ? 'open' : 'closed'}
        exit="closed"
        variants={sidebarVariants}
        className={clsx(
          'fixed inset-y-0 left-0 z-50 w-64 bg-white border-r border-neutral-200 shadow-lg',
          'lg:translate-x-0 lg:static lg:inset-0'
        )}
      >
        {/* Sidebar header */}
        <div className="flex items-center justify-between h-16 px-6 border-b border-neutral-200">
          <div className="flex items-center space-x-3">
            <div className="flex items-center justify-center w-8 h-8 bg-gradient-primary rounded-lg">
              <Signal className="w-5 h-5 text-white" />
            </div>
            <div>
              <h1 className="text-lg font-bold text-neutral-900">TelecomIMS</h1>
              <p className="text-xs text-neutral-500">Inventory Management</p>
            </div>
          </div>
          
          {/* Close button for mobile */}
          <button
            onClick={onClose}
            className="lg:hidden p-2 rounded-lg hover:bg-neutral-100 transition-colors"
          >
            <ChevronLeft className="w-5 h-5 text-neutral-600" />
          </button>
        </div>
        
        {/* User info */}
        <div className="p-6 border-b border-neutral-200">
          <div className="flex items-center space-x-3">
            <div className="flex items-center justify-center w-10 h-10 bg-primary-100 rounded-full">
              <span className="text-sm font-medium text-primary-700">
                {user?.firstName?.[0]}{user?.lastName?.[0]}
              </span>
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-neutral-900 truncate">
                {user?.firstName} {user?.lastName}
              </p>
              <p className="text-xs text-neutral-500 capitalize">
                {user?.role}
              </p>
            </div>
          </div>
        </div>
        
        {/* Navigation */}
        <nav className="flex-1 px-4 py-6 space-y-2 overflow-y-auto custom-scrollbar">
          {filteredNavigation.map((item) => {
            const Icon = item.icon
            return (
              <NavLink
                key={item.name}
                to={item.href}
                onClick={onClose}
                className={({ isActive }) =>
                  clsx(
                    'group flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200',
                    isActive
                      ? 'bg-primary-50 text-primary-700 border-r-2 border-primary-600'
                      : 'text-neutral-700 hover:bg-neutral-100 hover:text-neutral-900'
                  )
                }
              >
                {({ isActive }) => (
                  <>
                    <Icon
                      className={clsx(
                        'mr-3 h-5 w-5 transition-colors',
                        isActive
                          ? 'text-primary-600'
                          : 'text-neutral-400 group-hover:text-neutral-600'
                      )}
                    />
                    <span>{item.name}</span>
                    {isActive && (
                      <motion.div
                        layoutId="activeTab"
                        className="ml-auto w-1 h-6 bg-primary-600 rounded-full"
                        initial={false}
                        transition={{ type: 'spring', stiffness: 500, damping: 30 }}
                      />
                    )}
                  </>
                )}
              </NavLink>
            )
          })}
        </nav>
        
        {/* Footer */}
        <div className="p-4 border-t border-neutral-200">
          <div className="flex items-center justify-center space-x-2 text-xs text-neutral-500">
            <Zap className="w-4 h-4 text-primary-500" />
            <span>Powered by TelecomIMS</span>
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  )
}

export default Sidebar
