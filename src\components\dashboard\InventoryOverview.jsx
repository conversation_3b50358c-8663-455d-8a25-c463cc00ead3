import React from 'react'
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, ResponsiveC<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from 'recharts'
import Card from '@components/ui/Card'
import { Package, Layers } from 'lucide-react'

const InventoryOverview = () => {
  // Mock data for inventory distribution by category
  const categoryData = [
    { name: 'Network Equipment', value: 35, count: 437, color: '#3b82f6' },
    { name: 'Mobile Devices', value: 25, count: 312, color: '#06b6d4' },
    { name: 'Cables & Accessories', value: 20, count: 249, color: '#22c55e' },
    { name: 'Antennas', value: 12, count: 150, color: '#f59e0b' },
    { name: 'Power Equipment', value: 8, count: 99, color: '#ef4444' }
  ]
  
  // Mock data for stock status
  const statusData = [
    { name: 'In Stock', value: 78, count: 973, color: '#22c55e' },
    { name: 'Low Stock', value: 15, count: 187, color: '#f59e0b' },
    { name: 'Out of Stock', value: 5, count: 62, color: '#ef4444' },
    { name: 'Reserved', value: 2, count: 25, color: '#8b5cf6' }
  ]
  
  const [activeView, setActiveView] = React.useState('category')
  
  const currentData = activeView === 'category' ? categoryData : statusData
  
  const CustomTooltip = ({ active, payload }) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload
      return (
        <div className="bg-white p-3 border border-neutral-200 rounded-lg shadow-lg">
          <p className="font-medium text-neutral-900">{data.name}</p>
          <p className="text-sm text-neutral-600">
            {data.count} items ({data.value}%)
          </p>
        </div>
      )
    }
    return null
  }
  
  const CustomLegend = ({ payload }) => {
    return (
      <div className="flex flex-wrap gap-2 mt-4">
        {payload.map((entry, index) => (
          <div key={index} className="flex items-center space-x-2">
            <div 
              className="w-3 h-3 rounded-full"
              style={{ backgroundColor: entry.color }}
            />
            <span className="text-sm text-neutral-600">{entry.value}</span>
          </div>
        ))}
      </div>
    )
  }
  
  return (
    <Card>
      <Card.Header>
        <div className="flex items-center justify-between">
          <div>
            <Card.Title className="flex items-center">
              <Layers className="w-5 h-5 mr-2 text-primary-600" />
              Inventory Overview
            </Card.Title>
            <Card.Description>
              Distribution of inventory by {activeView === 'category' ? 'category' : 'stock status'}
            </Card.Description>
          </div>
          
          <div className="flex bg-neutral-100 rounded-lg p-1">
            <button
              onClick={() => setActiveView('category')}
              className={`px-3 py-1 text-sm font-medium rounded transition-colors ${
                activeView === 'category'
                  ? 'bg-white text-primary-600 shadow-sm'
                  : 'text-neutral-600 hover:text-neutral-900'
              }`}
            >
              Category
            </button>
            <button
              onClick={() => setActiveView('status')}
              className={`px-3 py-1 text-sm font-medium rounded transition-colors ${
                activeView === 'status'
                  ? 'bg-white text-primary-600 shadow-sm'
                  : 'text-neutral-600 hover:text-neutral-900'
              }`}
            >
              Status
            </button>
          </div>
        </div>
      </Card.Header>
      
      <div className="h-80">
        <ResponsiveContainer width="100%" height="100%">
          <PieChart>
            <Pie
              data={currentData}
              cx="50%"
              cy="50%"
              innerRadius={60}
              outerRadius={120}
              paddingAngle={2}
              dataKey="value"
            >
              {currentData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.color} />
              ))}
            </Pie>
            <Tooltip content={<CustomTooltip />} />
            <Legend content={<CustomLegend />} />
          </PieChart>
        </ResponsiveContainer>
      </div>
      
      {/* Summary Stats */}
      <div className="grid grid-cols-2 gap-4 mt-6 pt-6 border-t border-neutral-200">
        <div className="text-center">
          <p className="text-2xl font-bold text-neutral-900">
            {currentData.reduce((sum, item) => sum + item.count, 0)}
          </p>
          <p className="text-sm text-neutral-600">Total Items</p>
        </div>
        <div className="text-center">
          <p className="text-2xl font-bold text-neutral-900">
            {currentData.length}
          </p>
          <p className="text-sm text-neutral-600">
            {activeView === 'category' ? 'Categories' : 'Status Types'}
          </p>
        </div>
      </div>
    </Card>
  )
}

export default InventoryOverview
