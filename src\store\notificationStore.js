import { create } from 'zustand'
import { notificationService } from '@services/notificationService'
import { NOTIFICATION_TYPES, NOTIFICATION_PRIORITY } from '@types'
import toast from 'react-hot-toast'

const useNotificationStore = create((set, get) => ({
  // State
  notifications: [],
  unreadCount: 0,
  isLoading: false,
  error: null,
  realTimeEnabled: true,
  
  // Actions
  fetchNotifications: async (params = {}) => {
    set({ isLoading: true, error: null })
    
    try {
      const response = await notificationService.getNotifications(params)
      const { notifications, unreadCount } = response.data
      
      set({
        notifications,
        unreadCount,
        isLoading: false,
        error: null
      })
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Failed to fetch notifications'
      set({
        isLoading: false,
        error: errorMessage
      })
    }
  },
  
  markAsRead: async (notificationId) => {
    try {
      await notificationService.markAsRead(notificationId)
      
      set((state) => ({
        notifications: state.notifications.map(notification =>
          notification.id === notificationId
            ? { ...notification, isRead: true }
            : notification
        ),
        unreadCount: Math.max(0, state.unreadCount - 1)
      }))
    } catch (error) {
      console.error('Failed to mark notification as read:', error)
    }
  },
  
  markAllAsRead: async () => {
    try {
      await notificationService.markAllAsRead()
      
      set((state) => ({
        notifications: state.notifications.map(notification => ({
          ...notification,
          isRead: true
        })),
        unreadCount: 0
      }))
      
      toast.success('All notifications marked as read')
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Failed to mark all as read'
      toast.error(errorMessage)
    }
  },
  
  deleteNotification: async (notificationId) => {
    try {
      await notificationService.deleteNotification(notificationId)
      
      set((state) => {
        const notification = state.notifications.find(n => n.id === notificationId)
        const wasUnread = notification && !notification.isRead
        
        return {
          notifications: state.notifications.filter(n => n.id !== notificationId),
          unreadCount: wasUnread ? Math.max(0, state.unreadCount - 1) : state.unreadCount
        }
      })
      
      toast.success('Notification deleted')
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Failed to delete notification'
      toast.error(errorMessage)
    }
  },
  
  clearAllNotifications: async () => {
    try {
      await notificationService.clearAll()
      
      set({
        notifications: [],
        unreadCount: 0
      })
      
      toast.success('All notifications cleared')
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Failed to clear notifications'
      toast.error(errorMessage)
    }
  },
  
  createNotification: async (notificationData) => {
    try {
      const response = await notificationService.createNotification(notificationData)
      const newNotification = response.data
      
      set((state) => ({
        notifications: [newNotification, ...state.notifications],
        unreadCount: state.unreadCount + 1
      }))
      
      // Show toast for high priority notifications
      if (newNotification.priority === NOTIFICATION_PRIORITY.HIGH || 
          newNotification.priority === NOTIFICATION_PRIORITY.CRITICAL) {
        toast(newNotification.message, {
          icon: getNotificationIcon(newNotification.type),
          duration: 6000
        })
      }
      
      return newNotification
    } catch (error) {
      console.error('Failed to create notification:', error)
      return null
    }
  },
  
  // Real-time notification handling
  addRealTimeNotification: (notification) => {
    set((state) => ({
      notifications: [notification, ...state.notifications],
      unreadCount: state.unreadCount + 1
    }))
    
    // Show toast for real-time notifications
    if (get().realTimeEnabled) {
      toast(notification.message, {
        icon: getNotificationIcon(notification.type),
        duration: notification.priority === NOTIFICATION_PRIORITY.CRITICAL ? 8000 : 4000
      })
    }
  },
  
  updateNotificationSettings: async (settings) => {
    try {
      await notificationService.updateSettings(settings)
      
      set((state) => ({
        realTimeEnabled: settings.realTimeEnabled ?? state.realTimeEnabled
      }))
      
      toast.success('Notification settings updated')
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Failed to update settings'
      toast.error(errorMessage)
    }
  },
  
  // Utility functions
  getNotificationsByType: (type) => {
    const { notifications } = get()
    return notifications.filter(notification => notification.type === type)
  },
  
  getUnreadNotifications: () => {
    const { notifications } = get()
    return notifications.filter(notification => !notification.isRead)
  },
  
  getHighPriorityNotifications: () => {
    const { notifications } = get()
    return notifications.filter(notification => 
      notification.priority === NOTIFICATION_PRIORITY.HIGH ||
      notification.priority === NOTIFICATION_PRIORITY.CRITICAL
    )
  },
  
  toggleRealTime: () => {
    set((state) => ({
      realTimeEnabled: !state.realTimeEnabled
    }))
  },
  
  clearError: () => {
    set({ error: null })
  }
}))

// Helper function to get notification icons
const getNotificationIcon = (type) => {
  switch (type) {
    case NOTIFICATION_TYPES.LOW_STOCK:
      return '📦'
    case NOTIFICATION_TYPES.OUT_OF_STOCK:
      return '🚨'
    case NOTIFICATION_TYPES.ORDER_UPDATE:
      return '📋'
    case NOTIFICATION_TYPES.SYSTEM_ALERT:
      return '⚠️'
    case NOTIFICATION_TYPES.USER_ACTION:
      return '👤'
    case NOTIFICATION_TYPES.SUPPLIER_UPDATE:
      return '🏢'
    default:
      return '🔔'
  }
}

export { useNotificationStore }
