import { apiHelpers, endpoints } from './api'

export const notificationService = {
  // Get notifications with pagination
  getNotifications: (params = {}) => {
    return apiHelpers.get(endpoints.notifications.list, params)
  },
  
  // Create new notification
  createNotification: (notificationData) => {
    return apiHelpers.post(endpoints.notifications.create, notificationData)
  },
  
  // Mark notification as read
  markAsRead: (id) => {
    return apiHelpers.patch(endpoints.notifications.markRead(id))
  },
  
  // Mark all notifications as read
  markAllAsRead: () => {
    return apiHelpers.patch(endpoints.notifications.markAllRead)
  },
  
  // Delete notification
  deleteNotification: (id) => {
    return apiHelpers.delete(endpoints.notifications.delete(id))
  },
  
  // Clear all notifications
  clearAll: () => {
    return apiHelpers.delete(endpoints.notifications.clear)
  },
  
  // Get notification settings
  getSettings: () => {
    return apiHelpers.get(endpoints.notifications.settings)
  },
  
  // Update notification settings
  updateSettings: (settings) => {
    return apiHelpers.put(endpoints.notifications.settings, settings)
  },
  
  // Get unread count
  getUnreadCount: () => {
    return apiHelpers.get(`${endpoints.notifications.list}/unread-count`)
  }
}
