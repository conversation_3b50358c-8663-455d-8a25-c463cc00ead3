import React from 'react'
import { motion } from 'framer-motion'
import { Settings as SettingsIcon, Save, RefreshCw } from 'lucide-react'
import Card from '@components/ui/Card'
import Button from '@components/ui/Button'

const Settings = () => {
  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-between"
      >
        <div>
          <h1 className="text-3xl font-bold text-neutral-900 mb-2">
            System Settings
          </h1>
          <p className="text-neutral-600">
            Configure system preferences and application settings
          </p>
        </div>
        
        <div className="flex items-center space-x-3">
          <Button variant="outline" icon={<RefreshCw className="w-4 h-4" />}>
            Reset
          </Button>
          <Button
            icon={<Save className="w-4 h-4" />}
            className="bg-gradient-primary"
          >
            Save Changes
          </Button>
        </div>
      </motion.div>
      
      {/* Coming Soon */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <Card className="text-center py-16">
          <SettingsIcon className="w-16 h-16 text-primary-600 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-neutral-900 mb-2">
            System Settings Coming Soon
          </h3>
          <p className="text-neutral-600 mb-6 max-w-md mx-auto">
            This section will include system configuration, notification preferences, 
            backup settings, and application customization options.
          </p>
          <div className="flex justify-center space-x-4">
            <Button variant="outline">View Current Settings</Button>
            <Button>Configure System</Button>
          </div>
        </Card>
      </motion.div>
    </div>
  )
}

export default Settings
