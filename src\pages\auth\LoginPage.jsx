import React from 'react'
import { motion } from 'framer-motion'
import { useForm } from 'react-hook-form'
import { Navigate } from 'react-router-dom'
import { Signal, Mail, Lock, Eye, EyeOff } from 'lucide-react'
import { useAuthStore } from '@store/authStore'
import Button from '@components/ui/Button'
import Input from '@components/ui/Input'
import Card from '@components/ui/Card'

const LoginPage = () => {
  const { login, isLoading, error, isAuthenticated } = useAuthStore()
  const [showPassword, setShowPassword] = React.useState(false)
  
  const {
    register,
    handleSubmit,
    formState: { errors },
    setError
  } = useForm()
  
  // Redirect if already authenticated
  if (isAuthenticated) {
    return <Navigate to="/dashboard" replace />
  }
  
  const onSubmit = async (data) => {
    const result = await login(data)
    
    if (!result.success) {
      setError('root', {
        type: 'manual',
        message: result.error
      })
    }
  }
  
  // Demo credentials for testing
  const demoCredentials = [
    { email: '<EMAIL>', password: 'admin123', role: 'Admin' },
    { email: '<EMAIL>', password: 'manager123', role: 'Manager' },
    { email: '<EMAIL>', password: 'staff123', role: 'Staff' }
  ]
  
  const fillDemoCredentials = (email, password) => {
    document.getElementById('email').value = email
    document.getElementById('password').value = password
  }
  
  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 via-white to-secondary-50 flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-md"
      >
        {/* Logo and Title */}
        <div className="text-center mb-8">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, type: 'spring', stiffness: 200 }}
            className="inline-flex items-center justify-center w-16 h-16 bg-gradient-primary rounded-2xl shadow-lg mb-4"
          >
            <Signal className="w-8 h-8 text-white" />
          </motion.div>
          
          <motion.h1
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3 }}
            className="text-3xl font-bold text-gradient mb-2"
          >
            TelecomIMS
          </motion.h1>
          
          <motion.p
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.4 }}
            className="text-neutral-600"
          >
            Inventory Management System
          </motion.p>
        </div>
        
        {/* Login Form */}
        <Card className="shadow-strong">
          <Card.Header>
            <Card.Title>Sign in to your account</Card.Title>
            <Card.Description>
              Enter your credentials to access the inventory management system
            </Card.Description>
          </Card.Header>
          
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            {/* Email Field */}
            <Input
              id="email"
              label="Email address"
              type="email"
              placeholder="Enter your email"
              icon={<Mail className="w-4 h-4" />}
              error={errors.email?.message}
              {...register('email', {
                required: 'Email is required',
                pattern: {
                  value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                  message: 'Invalid email address'
                }
              })}
            />
            
            {/* Password Field */}
            <div className="relative">
              <Input
                id="password"
                label="Password"
                type={showPassword ? 'text' : 'password'}
                placeholder="Enter your password"
                icon={<Lock className="w-4 h-4" />}
                error={errors.password?.message}
                {...register('password', {
                  required: 'Password is required',
                  minLength: {
                    value: 6,
                    message: 'Password must be at least 6 characters'
                  }
                })}
              />
              
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-9 text-neutral-400 hover:text-neutral-600"
              >
                {showPassword ? (
                  <EyeOff className="w-4 h-4" />
                ) : (
                  <Eye className="w-4 h-4" />
                )}
              </button>
            </div>
            
            {/* Error Message */}
            {(error || errors.root) && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                className="p-3 bg-danger-50 border border-danger-200 rounded-lg"
              >
                <p className="text-sm text-danger-700">
                  {error || errors.root?.message}
                </p>
              </motion.div>
            )}
            
            {/* Submit Button */}
            <Button
              type="submit"
              fullWidth
              loading={isLoading}
              className="bg-gradient-primary hover:from-primary-700 hover:to-primary-800"
            >
              {isLoading ? 'Signing in...' : 'Sign in'}
            </Button>
          </form>
          
          {/* Demo Credentials */}
          <Card.Footer>
            <div className="text-center">
              <p className="text-sm text-neutral-600 mb-3">Demo Credentials:</p>
              <div className="grid gap-2">
                {demoCredentials.map((cred, index) => (
                  <button
                    key={index}
                    type="button"
                    onClick={() => fillDemoCredentials(cred.email, cred.password)}
                    className="text-xs text-left p-2 bg-neutral-50 hover:bg-neutral-100 rounded border transition-colors"
                  >
                    <div className="font-medium text-neutral-700">{cred.role}</div>
                    <div className="text-neutral-500">{cred.email}</div>
                  </button>
                ))}
              </div>
            </div>
          </Card.Footer>
        </Card>
        
        {/* Footer */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.6 }}
          className="text-center mt-8"
        >
          <p className="text-sm text-neutral-500">
            © 2024 TelecomIMS. All rights reserved.
          </p>
        </motion.div>
      </motion.div>
    </div>
  )
}

export default LoginPage
