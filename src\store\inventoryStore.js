import { create } from 'zustand'
import { inventoryService } from '@services/inventoryService'
import { INVENTORY_STATUS, MOVEMENT_TYPES } from '@types'
import toast from 'react-hot-toast'

const useInventoryStore = create((set, get) => ({
  // State
  items: [],
  movements: [],
  locations: [],
  selectedItem: null,
  isLoading: false,
  error: null,
  filters: {
    status: '',
    category: '',
    location: '',
    search: ''
  },
  pagination: {
    page: 1,
    limit: 20,
    total: 0
  },
  
  // Actions
  fetchInventoryItems: async (params = {}) => {
    set({ isLoading: true, error: null })
    
    try {
      const response = await inventoryService.getInventoryItems(params)
      const { items, pagination } = response.data
      
      set({
        items,
        pagination,
        isLoading: false,
        error: null
      })
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Failed to fetch inventory items'
      set({
        isLoading: false,
        error: errorMessage
      })
      toast.error(errorMessage)
    }
  },
  
  fetchInventoryItem: async (id) => {
    set({ isLoading: true, error: null })
    
    try {
      const response = await inventoryService.getInventoryItem(id)
      const item = response.data
      
      set({
        selectedItem: item,
        isLoading: false,
        error: null
      })
      
      return item
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Failed to fetch inventory item'
      set({
        isLoading: false,
        error: errorMessage
      })
      toast.error(errorMessage)
      return null
    }
  },
  
  updateInventoryItem: async (id, data) => {
    set({ isLoading: true, error: null })
    
    try {
      const response = await inventoryService.updateInventoryItem(id, data)
      const updatedItem = response.data
      
      set((state) => ({
        items: state.items.map(item => 
          item.id === id ? updatedItem : item
        ),
        selectedItem: state.selectedItem?.id === id ? updatedItem : state.selectedItem,
        isLoading: false,
        error: null
      }))
      
      toast.success('Inventory item updated successfully')
      return updatedItem
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Failed to update inventory item'
      set({
        isLoading: false,
        error: errorMessage
      })
      toast.error(errorMessage)
      return null
    }
  },
  
  adjustStock: async (id, adjustment) => {
    set({ isLoading: true, error: null })
    
    try {
      const response = await inventoryService.adjustStock(id, adjustment)
      const updatedItem = response.data
      
      set((state) => ({
        items: state.items.map(item => 
          item.id === id ? updatedItem : item
        ),
        selectedItem: state.selectedItem?.id === id ? updatedItem : state.selectedItem,
        isLoading: false,
        error: null
      }))
      
      toast.success(`Stock ${adjustment.type} recorded successfully`)
      return updatedItem
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Failed to adjust stock'
      set({
        isLoading: false,
        error: errorMessage
      })
      toast.error(errorMessage)
      return null
    }
  },
  
  transferStock: async (transferData) => {
    set({ isLoading: true, error: null })
    
    try {
      const response = await inventoryService.transferStock(transferData)
      const result = response.data
      
      // Refresh inventory items
      get().fetchInventoryItems()
      
      set({
        isLoading: false,
        error: null
      })
      
      toast.success('Stock transfer completed successfully')
      return result
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Failed to transfer stock'
      set({
        isLoading: false,
        error: errorMessage
      })
      toast.error(errorMessage)
      return null
    }
  },
  
  fetchMovements: async (params = {}) => {
    set({ isLoading: true, error: null })
    
    try {
      const response = await inventoryService.getMovements(params)
      const movements = response.data
      
      set({
        movements,
        isLoading: false,
        error: null
      })
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Failed to fetch movements'
      set({
        isLoading: false,
        error: errorMessage
      })
      toast.error(errorMessage)
    }
  },
  
  fetchLocations: async () => {
    try {
      const response = await inventoryService.getLocations()
      const locations = response.data
      
      set({ locations })
    } catch (error) {
      console.error('Failed to fetch locations:', error)
    }
  },
  
  // Utility functions
  getLowStockItems: () => {
    const { items } = get()
    return items.filter(item => 
      item.status === INVENTORY_STATUS.LOW_STOCK || 
      item.currentStock <= item.reorderPoint
    )
  },
  
  getOutOfStockItems: () => {
    const { items } = get()
    return items.filter(item => 
      item.status === INVENTORY_STATUS.OUT_OF_STOCK || 
      item.currentStock === 0
    )
  },
  
  getTotalValue: () => {
    const { items } = get()
    return items.reduce((total, item) => 
      total + (item.currentStock * item.unitCost), 0
    )
  },
  
  getItemsByCategory: (category) => {
    const { items } = get()
    return items.filter(item => item.category === category)
  },
  
  getItemsByLocation: (locationId) => {
    const { items } = get()
    return items.filter(item => item.locationId === locationId)
  },
  
  // Filters and search
  setFilters: (newFilters) => {
    set((state) => ({
      filters: { ...state.filters, ...newFilters }
    }))
  },
  
  clearFilters: () => {
    set({
      filters: {
        status: '',
        category: '',
        location: '',
        search: ''
      }
    })
  },
  
  setPagination: (newPagination) => {
    set((state) => ({
      pagination: { ...state.pagination, ...newPagination }
    }))
  },
  
  clearError: () => {
    set({ error: null })
  },
  
  clearSelectedItem: () => {
    set({ selectedItem: null })
  }
}))

export { useInventoryStore }
